<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require counselor or admin login
if (!hasRole(ROLE_COUNSELOR) && !hasRole(ROLE_ADMIN)) {
    requireRole(ROLE_COUNSELOR);
}

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Handle actions
$message = '';
$messageType = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'add_student':
                $username = sanitizeInput($_POST['username']);
                $email = sanitizeInput($_POST['email']);
                $full_name = sanitizeInput($_POST['full_name']);
                $phone = sanitizeInput($_POST['phone'] ?? '');
                $password = $_POST['password'];
                
                if (empty($username) || empty($email) || empty($full_name) || empty($password)) {
                    throw new Exception('يرجى ملء جميع الحقول المطلوبة');
                }
                
                if (!isValidEmail($email)) {
                    throw new Exception('البريد الإلكتروني غير صحيح');
                }
                
                $insertStmt = $pdo->prepare("
                    INSERT INTO users (username, email, password_hash, full_name, role, phone) 
                    VALUES (?, ?, ?, ?, 'student', ?)
                ");
                $insertStmt->execute([
                    $username, $email, hashPassword($password), $full_name, $phone
                ]);
                
                $message = 'تم إضافة الطالب بنجاح';
                $messageType = 'success';
                break;
                
            case 'toggle_status':
                $studentId = (int)$_POST['student_id'];
                $newStatus = (int)$_POST['new_status'];
                
                $updateStmt = $pdo->prepare("UPDATE users SET is_active = ? WHERE id = ? AND role = 'student'");
                $updateStmt->execute([$newStatus, $studentId]);
                
                $message = $newStatus ? 'تم تفعيل الطالب' : 'تم إلغاء تفعيل الطالب';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get search and filter parameters
$search = sanitizeInput($_GET['search'] ?? '');
$status = $_GET['status'] ?? 'all';
$page = max(1, (int)($_GET['page'] ?? 1));
$limit = ITEMS_PER_PAGE;
$offset = ($page - 1) * $limit;

// Build query
$whereConditions = ["u.role = 'student'"];
$params = [];

if (!empty($search)) {
    $whereConditions[] = "(u.full_name LIKE ? OR u.username LIKE ? OR u.email LIKE ?)";
    $searchParam = "%$search%";
    $params = array_merge($params, [$searchParam, $searchParam, $searchParam]);
}

if ($status !== 'all') {
    $whereConditions[] = "u.is_active = ?";
    $params[] = $status === 'active' ? 1 : 0;
}

$whereClause = implode(' AND ', $whereConditions);

// Get total count
$countStmt = $pdo->prepare("
    SELECT COUNT(*) as total 
    FROM users u 
    WHERE $whereClause
");
$countStmt->execute($params);
$totalStudents = $countStmt->fetch()['total'];
$totalPages = ceil($totalStudents / $limit);

// Get students with additional info
$studentsStmt = $pdo->prepare("
    SELECT u.*, 
           (SELECT COUNT(*) FROM student_preferences sp WHERE sp.student_id = u.id) as preferences_count,
           (SELECT COUNT(*) FROM student_questionnaire sq WHERE sq.student_id = u.id) as questionnaire_completed,
           (SELECT COUNT(*) FROM final_decisions fd WHERE fd.student_id = u.id) as has_decision
    FROM users u 
    WHERE $whereClause
    ORDER BY u.created_at DESC
    LIMIT $limit OFFSET $offset
");
$studentsStmt->execute($params);
$students = $studentsStmt->fetchAll();

$pageTitle = 'إدارة الطلاب';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="flex items-center justify-between mb-8" data-aos="fade-down">
            <div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إدارة الطلاب</h1>
                <p class="text-gray-600">عرض وإدارة بيانات الطلاب وحالة التوجيه</p>
            </div>
            <button onclick="showAddStudentModal()" class="btn btn-primary">
                <i class="fas fa-plus ml-2"></i>
                إضافة طالب جديد
            </button>
        </div>

        <!-- Flash Message -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> mb-6" data-aos="fade-up">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?> ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Filters and Search -->
        <div class="card mb-6" data-aos="fade-up">
            <div class="card-header">
                <h3 class="card-title">البحث والتصفية</h3>
            </div>
            <form method="GET" class="flex flex-wrap items-center gap-4">
                <div class="flex-1 min-w-64">
                    <input 
                        type="text" 
                        name="search" 
                        class="form-control" 
                        placeholder="البحث بالاسم أو اسم المستخدم أو البريد الإلكتروني..."
                        value="<?php echo htmlspecialchars($search); ?>"
                    >
                </div>
                <div>
                    <select name="status" class="form-control">
                        <option value="all" <?php echo $status === 'all' ? 'selected' : ''; ?>>جميع الطلاب</option>
                        <option value="active" <?php echo $status === 'active' ? 'selected' : ''; ?>>النشطون</option>
                        <option value="inactive" <?php echo $status === 'inactive' ? 'selected' : ''; ?>>غير النشطين</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search ml-2"></i>
                    بحث
                </button>
                <a href="students.php" class="btn btn-outline">
                    <i class="fas fa-times ml-2"></i>
                    إلغاء
                </a>
            </form>
        </div>

        <!-- Students Table -->
        <div class="card" data-aos="fade-up" data-aos-delay="100">
            <div class="card-header">
                <div class="flex items-center justify-between">
                    <h3 class="card-title">قائمة الطلاب (<?php echo $totalStudents; ?>)</h3>
                    <div class="flex items-center space-x-2 space-x-reverse">
                        <a href="import-students.php" class="btn btn-outline btn-sm">
                            <i class="fas fa-upload ml-1"></i>
                            استيراد CSV
                        </a>
                        <button onclick="exportStudents()" class="btn btn-secondary btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير
                        </button>
                    </div>
                </div>
            </div>
            
            <?php if (!empty($students)): ?>
                <div class="overflow-x-auto">
                    <table class="table">
                        <thead>
                            <tr>
                                <th>الطالب</th>
                                <th>معلومات الاتصال</th>
                                <th>حالة التوجيه</th>
                                <th>تاريخ التسجيل</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($students as $student): ?>
                                <tr>
                                    <td>
                                        <div class="flex items-center">
                                            <div class="w-10 h-10 bg-gradient-to-r from-blue-600 to-teal-600 rounded-full flex items-center justify-center text-white font-bold ml-3">
                                                <?php echo mb_substr($student['full_name'], 0, 1, 'UTF-8'); ?>
                                            </div>
                                            <div>
                                                <p class="font-semibold"><?php echo htmlspecialchars($student['full_name']); ?></p>
                                                <p class="text-sm text-gray-600">@<?php echo htmlspecialchars($student['username']); ?></p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <p class="text-sm"><?php echo htmlspecialchars($student['email']); ?></p>
                                        <?php if ($student['phone']): ?>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($student['phone']); ?></p>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="space-y-1">
                                            <div class="flex items-center">
                                                <i class="fas fa-list-ol text-xs ml-1 <?php echo $student['preferences_count'] > 0 ? 'text-green-600' : 'text-red-600'; ?>"></i>
                                                <span class="text-xs">الرغبات: <?php echo $student['preferences_count']; ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-clipboard-list text-xs ml-1 <?php echo $student['questionnaire_completed'] > 0 ? 'text-green-600' : 'text-red-600'; ?>"></i>
                                                <span class="text-xs">الاستبيان: <?php echo $student['questionnaire_completed'] > 0 ? 'مكتمل' : 'غير مكتمل'; ?></span>
                                            </div>
                                            <div class="flex items-center">
                                                <i class="fas fa-check-circle text-xs ml-1 <?php echo $student['has_decision'] > 0 ? 'text-green-600' : 'text-gray-400'; ?>"></i>
                                                <span class="text-xs">القرار: <?php echo $student['has_decision'] > 0 ? 'متخذ' : 'معلق'; ?></span>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <span class="text-sm"><?php echo formatDate($student['created_at']); ?></span>
                                    </td>
                                    <td>
                                        <span class="px-2 py-1 rounded-full text-xs font-medium <?php echo $student['is_active'] ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'; ?>">
                                            <?php echo $student['is_active'] ? 'نشط' : 'غير نشط'; ?>
                                        </span>
                                    </td>
                                    <td>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <a href="student-details.php?id=<?php echo $student['id']; ?>" class="text-blue-600 hover:text-blue-800" title="عرض التفاصيل">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit-student.php?id=<?php echo $student['id']; ?>" class="text-green-600 hover:text-green-800" title="تعديل">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <button onclick="toggleStudentStatus(<?php echo $student['id']; ?>, <?php echo $student['is_active'] ? 0 : 1; ?>)" 
                                                    class="<?php echo $student['is_active'] ? 'text-red-600 hover:text-red-800' : 'text-green-600 hover:text-green-800'; ?>" 
                                                    title="<?php echo $student['is_active'] ? 'إلغاء التفعيل' : 'تفعيل'; ?>">
                                                <i class="fas fa-<?php echo $student['is_active'] ? 'ban' : 'check'; ?>"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <?php if ($totalPages > 1): ?>
                    <div class="flex items-center justify-between p-4 border-t">
                        <div class="text-sm text-gray-600">
                            عرض <?php echo $offset + 1; ?> إلى <?php echo min($offset + $limit, $totalStudents); ?> من <?php echo $totalStudents; ?> طالب
                        </div>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <?php if ($page > 1): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page - 1])); ?>" class="btn btn-outline btn-sm">
                                    <i class="fas fa-chevron-right ml-1"></i>
                                    السابق
                                </a>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $i])); ?>" 
                                   class="btn <?php echo $i === $page ? 'btn-primary' : 'btn-outline'; ?> btn-sm">
                                    <?php echo $i; ?>
                                </a>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <a href="?<?php echo http_build_query(array_merge($_GET, ['page' => $page + 1])); ?>" class="btn btn-outline btn-sm">
                                    التالي
                                    <i class="fas fa-chevron-left mr-1"></i>
                                </a>
                            <?php endif; ?>
                        </div>
                    </div>
                <?php endif; ?>
            <?php else: ?>
                <div class="text-center py-12 text-gray-500">
                    <i class="fas fa-users text-6xl mb-4"></i>
                    <h3 class="text-xl font-semibold mb-2">لا توجد طلاب</h3>
                    <p class="mb-4">لم يتم العثور على طلاب مطابقين لمعايير البحث</p>
                    <button onclick="showAddStudentModal()" class="btn btn-primary">
                        <i class="fas fa-plus ml-2"></i>
                        إضافة طالب جديد
                    </button>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Add Student Modal -->
<div id="addStudentModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
    <div class="bg-white rounded-lg p-6 w-full max-w-md mx-4">
        <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold">إضافة طالب جديد</h3>
            <button onclick="hideAddStudentModal()" class="text-gray-400 hover:text-gray-600">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <form method="POST" id="addStudentForm">
            <input type="hidden" name="action" value="add_student">
            
            <div class="form-group">
                <label for="username" class="form-label">اسم المستخدم *</label>
                <input type="text" id="username" name="username" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="email" class="form-label">البريد الإلكتروني *</label>
                <input type="email" id="email" name="email" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="full_name" class="form-label">الاسم الكامل *</label>
                <input type="text" id="full_name" name="full_name" class="form-control" required>
            </div>
            
            <div class="form-group">
                <label for="phone" class="form-label">رقم الهاتف</label>
                <input type="tel" id="phone" name="phone" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="password" class="form-label">كلمة المرور *</label>
                <input type="password" id="password" name="password" class="form-control" required>
            </div>
            
            <div class="flex justify-end space-x-3 space-x-reverse">
                <button type="button" onclick="hideAddStudentModal()" class="btn btn-outline">إلغاء</button>
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save ml-2"></i>
                    حفظ
                </button>
            </div>
        </form>
    </div>
</div>

<?php 
$inlineJS = '
function showAddStudentModal() {
    document.getElementById("addStudentModal").classList.remove("hidden");
    document.getElementById("addStudentModal").classList.add("flex");
}

function hideAddStudentModal() {
    document.getElementById("addStudentModal").classList.add("hidden");
    document.getElementById("addStudentModal").classList.remove("flex");
    document.getElementById("addStudentForm").reset();
}

function toggleStudentStatus(studentId, newStatus) {
    const action = newStatus ? "تفعيل" : "إلغاء تفعيل";
    if (confirm(`هل أنت متأكد من ${action} هذا الطالب؟`)) {
        const form = document.createElement("form");
        form.method = "POST";
        form.innerHTML = `
            <input type="hidden" name="action" value="toggle_status">
            <input type="hidden" name="student_id" value="${studentId}">
            <input type="hidden" name="new_status" value="${newStatus}">
        `;
        document.body.appendChild(form);
        form.submit();
    }
}

function exportStudents() {
    alert("وظيفة التصدير قيد التطوير");
}

// Form validation
document.getElementById("addStudentForm").addEventListener("submit", function(e) {
    const username = document.getElementById("username").value.trim();
    const email = document.getElementById("email").value.trim();
    const fullName = document.getElementById("full_name").value.trim();
    const password = document.getElementById("password").value;
    
    if (!username || !email || !fullName || !password) {
        e.preventDefault();
        alert("يرجى ملء جميع الحقول المطلوبة");
        return false;
    }
    
    showLoading();
});

// Close modal when clicking outside
document.getElementById("addStudentModal").addEventListener("click", function(e) {
    if (e.target === this) {
        hideAddStudentModal();
    }
});
';

include '../../includes/footer.php'; 
?>
