<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME; ?></title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="<?php echo SITE_URL; ?>/assets/css/style.css">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    
    <!-- AOS Animation Library -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <!-- Font Awesome for icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    
    <!-- Custom RTL adjustments for Tailwind -->
    <style>
        /* RTL adjustments for Tailwind CSS */
        .rtl {
            direction: rtl;
        }
        
        /* Custom Tailwind RTL utilities */
        .mr-auto-rtl {
            margin-left: auto;
            margin-right: 0;
        }
        
        .ml-auto-rtl {
            margin-right: auto;
            margin-left: 0;
        }
        
        .text-right-rtl {
            text-align: right;
        }
        
        .text-left-rtl {
            text-align: left;
        }
        
        /* Custom animations */
        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes fadeInRight {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .animate-fadeInUp {
            animation: fadeInUp 0.6s ease-out;
        }
        
        .animate-fadeInRight {
            animation: fadeInRight 0.6s ease-out;
        }
        
        /* Loading spinner */
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid var(--color-primary);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
    
    <?php if (isset($additionalCSS)): ?>
        <?php foreach ($additionalCSS as $css): ?>
            <link rel="stylesheet" href="<?php echo $css; ?>">
        <?php endforeach; ?>
    <?php endif; ?>
</head>
<body class="rtl">
    <!-- Flash Messages -->
    <?php
    $flash = getFlashMessage();
    if ($flash):
    ?>
    <div id="flashMessage" class="fixed top-4 right-4 z-50 max-w-md">
        <div class="alert alert-<?php echo $flash['type']; ?> shadow-lg">
            <div class="flex items-center">
                <i class="fas fa-<?php 
                    echo $flash['type'] === 'success' ? 'check-circle' : 
                        ($flash['type'] === 'error' ? 'exclamation-circle' : 
                        ($flash['type'] === 'warning' ? 'exclamation-triangle' : 'info-circle')); 
                ?> ml-2"></i>
                <span><?php echo htmlspecialchars($flash['message']); ?></span>
                <button onclick="closeFlashMessage()" class="mr-auto text-lg font-bold">&times;</button>
            </div>
        </div>
    </div>
    <script>
        function closeFlashMessage() {
            document.getElementById('flashMessage').style.display = 'none';
        }
        
        // Auto-hide flash message after 5 seconds
        setTimeout(closeFlashMessage, 5000);
    </script>
    <?php endif; ?>
    
    <!-- Loading overlay -->
    <div id="loadingOverlay" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden items-center justify-center">
        <div class="bg-white p-6 rounded-lg text-center">
            <div class="spinner mx-auto mb-4"></div>
            <p>جاري التحميل...</p>
        </div>
    </div>
