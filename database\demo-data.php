<?php
// Add demo data for testing
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../includes/functions.php';

try {
    $pdo = getDBConnection();
    
    // Insert demo users
    $users = [
        [
            'username' => 'counselor',
            'email' => '<EMAIL>',
            'password_hash' => hashPassword('password'),
            'full_name' => 'أحمد محمد - مستشار التوجيه',
            'role' => 'counselor',
            'phone' => '0123456789'
        ],
        [
            'username' => 'student1',
            'email' => '<EMAIL>',
            'password_hash' => hashPassword('password'),
            'full_name' => 'محمد علي أحمد',
            'role' => 'student',
            'phone' => '0123456780'
        ],
        [
            'username' => 'student2',
            'email' => '<EMAIL>',
            'password_hash' => hashPassword('password'),
            'full_name' => 'فاطمة حسن محمد',
            'role' => 'student',
            'phone' => '0123456781'
        ],
        [
            'username' => 'student3',
            'email' => '<EMAIL>',
            'password_hash' => hashPassword('password'),
            'full_name' => 'عبدالله سالم أحمد',
            'role' => 'student',
            'phone' => '0123456782'
        ]
    ];
    
    $insertUserStmt = $pdo->prepare("
        INSERT INTO users (username, email, password_hash, full_name, role, phone) 
        VALUES (?, ?, ?, ?, ?, ?)
    ");
    
    foreach ($users as $user) {
        try {
            $insertUserStmt->execute([
                $user['username'],
                $user['email'],
                $user['password_hash'],
                $user['full_name'],
                $user['role'],
                $user['phone']
            ]);
            echo "Created user: " . $user['username'] . " (" . $user['full_name'] . ")\n";
        } catch (PDOException $e) {
            if ($e->getCode() == 23000) { // Duplicate entry
                echo "User already exists: " . $user['username'] . "\n";
            } else {
                throw $e;
            }
        }
    }
    
    // Add some sample academic results for student1
    $student1Stmt = $pdo->prepare("SELECT id FROM users WHERE username = 'student1'");
    $student1Stmt->execute();
    $student1 = $student1Stmt->fetch();
    
    if ($student1) {
        $subjects = [
            ['الرياضيات', 16.5],
            ['الفيزياء', 15.0],
            ['الكيمياء', 14.5],
            ['علوم طبيعية', 15.5],
            ['اللغة العربية', 13.0],
            ['اللغة الإنجليزية', 14.0],
            ['التاريخ والجغرافيا', 12.5],
            ['الفلسفة', 13.5]
        ];
        
        $insertResultStmt = $pdo->prepare("
            INSERT INTO academic_results (student_id, subject, grade, semester, academic_year) 
            VALUES (?, ?, ?, 'annual', '2024-2025')
        ");
        
        foreach ($subjects as $subject) {
            try {
                $insertResultStmt->execute([$student1['id'], $subject[0], $subject[1]]);
                echo "Added grade for " . $subject[0] . ": " . $subject[1] . "\n";
            } catch (PDOException $e) {
                if ($e->getCode() != 23000) { // Not duplicate entry
                    throw $e;
                }
            }
        }
    }
    
    echo "Demo data created successfully!\n";
    echo "\nDemo accounts:\n";
    echo "Admin: admin / password\n";
    echo "Counselor: counselor / password\n";
    echo "Student 1: student1 / password\n";
    echo "Student 2: student2 / password\n";
    echo "Student 3: student3 / password\n";
    
} catch (Exception $e) {
    echo "Error creating demo data: " . $e->getMessage() . "\n";
}
?>
