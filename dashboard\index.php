<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

// Redirect based on user role
$currentUser = getCurrentUser();
if ($currentUser) {
    switch ($currentUser['role']) {
        case ROLE_ADMIN:
        case ROLE_COUNSELOR:
            header('Location: counselor/');
            break;
        case ROLE_STUDENT:
            header('Location: student/');
            break;
        default:
            // Fallback dashboard
            break;
    }
    exit();
}

$pageTitle = 'لوحة التحكم';
include '../includes/header.php';
?>

<div class="min-h-screen bg-gray-50">
    <!-- Navigation -->
    <nav class="bg-white shadow-sm border-b">
        <div class="container">
            <div class="flex justify-between items-center py-4">
                <div class="flex items-center">
                    <i class="fas fa-graduation-cap text-2xl text-blue-600 ml-3"></i>
                    <h1 class="text-xl font-bold text-gray-800">نظام توجيهي</h1>
                </div>
                <div class="flex items-center space-x-4 space-x-reverse">
                    <span class="text-gray-600">مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></span>
                    <a href="../auth/logout.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-sign-out-alt ml-1"></i>
                        تسجيل الخروج
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-8">
        <div class="text-center">
            <div class="card max-w-md mx-auto">
                <div class="text-6xl text-gray-400 mb-4">
                    <i class="fas fa-user-circle"></i>
                </div>
                <h2 class="text-2xl font-bold text-gray-800 mb-4">مرحباً بك في النظام</h2>
                <p class="text-gray-600 mb-6">
                    يتم توجيهك إلى لوحة التحكم المناسبة لدورك في النظام...
                </p>
                <div class="space-y-3">
                    <?php if ($currentUser['role'] === ROLE_ADMIN || $currentUser['role'] === ROLE_COUNSELOR): ?>
                        <a href="counselor/" class="btn btn-primary w-full">
                            <i class="fas fa-chalkboard-teacher ml-2"></i>
                            لوحة تحكم المستشار
                        </a>
                    <?php endif; ?>
                    
                    <?php if ($currentUser['role'] === ROLE_STUDENT): ?>
                        <a href="student/" class="btn btn-primary w-full">
                            <i class="fas fa-user-graduate ml-2"></i>
                            لوحة تحكم الطالب
                        </a>
                    <?php endif; ?>
                    
                    <a href="../" class="btn btn-outline w-full">
                        <i class="fas fa-home ml-2"></i>
                        الصفحة الرئيسية
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
