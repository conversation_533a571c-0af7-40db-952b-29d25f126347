<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require student login
requireRole(ROLE_STUDENT);

$currentUser = getCurrentUser();
$pdo = getDBConnection();
$studentId = $currentUser['id'];

// Get final decision
$decisionStmt = $pdo->prepare("
    SELECT fd.*, b.name as branch_name, b.description as branch_description,
           u.full_name as decided_by_name
    FROM final_decisions fd 
    JOIN branches b ON fd.assigned_branch_id = b.id 
    LEFT JOIN users u ON fd.decided_by = u.id
    WHERE fd.student_id = ?
");
$decisionStmt->execute([$studentId]);
$finalDecision = $decisionStmt->fetch();

// Get student preferences with current status
$preferencesStmt = $pdo->prepare("
    SELECT sp.*, b.name as branch_name, b.description as branch_description,
           b.available_seats
    FROM student_preferences sp 
    JOIN branches b ON sp.branch_id = b.id 
    WHERE sp.student_id = ? 
    ORDER BY sp.priority_order
");
$preferencesStmt->execute([$studentId]);
$preferences = $preferencesStmt->fetchAll();

// Get academic results
$resultsStmt = $pdo->prepare("
    SELECT * FROM academic_results 
    WHERE student_id = ? 
    ORDER BY subject
");
$resultsStmt->execute([$studentId]);
$academicResults = $resultsStmt->fetchAll();

// Calculate average
$totalGrades = 0;
$subjectCount = count($academicResults);
foreach ($academicResults as $result) {
    $totalGrades += $result['grade'];
}
$average = $subjectCount > 0 ? round($totalGrades / $subjectCount, 2) : 0;

// Get questionnaire completion status
$questionnaireStmt = $pdo->prepare("SELECT COUNT(*) as count FROM student_questionnaire WHERE student_id = ?");
$questionnaireStmt->execute([$studentId]);
$questionnaireCompleted = $questionnaireStmt->fetch()['count'] > 0;

// Get family survey status
$familySurveyStmt = $pdo->prepare("SELECT COUNT(*) as count FROM family_survey WHERE student_id = ?");
$familySurveyStmt->execute([$studentId]);
$familySurveyCompleted = $familySurveyStmt->fetch()['count'] > 0;

$pageTitle = 'النتائج';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">النتائج والتوجيه</h1>
                    <p class="text-gray-600">عرض نتائج التحليل والقرار النهائي للتوجيه</p>
                </div>
                <div class="text-5xl text-purple-600 opacity-20">
                    <i class="fas fa-poll"></i>
                </div>
            </div>
        </div>

        <?php if ($finalDecision): ?>
            <!-- Final Decision Card -->
            <div class="card mb-8 border-l-4 border-green-500" data-aos="fade-up">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="text-5xl text-green-600 ml-6">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-green-800 mb-2">تم اتخاذ القرار النهائي!</h2>
                            <p class="text-lg text-gray-700 mb-2">
                                تم توجيهك إلى شعبة: 
                                <strong class="text-green-700"><?php echo htmlspecialchars($finalDecision['branch_name']); ?></strong>
                            </p>
                            <p class="text-sm text-gray-600 mb-2">
                                <?php echo htmlspecialchars($finalDecision['branch_description']); ?>
                            </p>
                            <div class="flex items-center space-x-4 space-x-reverse text-sm text-gray-500">
                                <span>
                                    <i class="fas fa-calendar ml-1"></i>
                                    تاريخ القرار: <?php echo formatDate($finalDecision['decision_date']); ?>
                                </span>
                                <?php if ($finalDecision['algorithm_score']): ?>
                                    <span>
                                        <i class="fas fa-chart-line ml-1"></i>
                                        نقاط التحليل: <?php echo $finalDecision['algorithm_score']; ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                    <div class="text-right">
                        <button onclick="printResults()" class="btn btn-outline btn-sm mb-2">
                            <i class="fas fa-print ml-1"></i>
                            طباعة
                        </button>
                        <br>
                        <button onclick="exportToPDF()" class="btn btn-primary btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير PDF
                        </button>
                    </div>
                </div>
                
                <?php if ($finalDecision['manual_override'] && $finalDecision['override_reason']): ?>
                    <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <h4 class="font-semibold text-yellow-800 mb-2">
                            <i class="fas fa-info-circle ml-1"></i>
                            ملاحظة خاصة
                        </h4>
                        <p class="text-yellow-700"><?php echo htmlspecialchars($finalDecision['override_reason']); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        <?php else: ?>
            <!-- Pending Decision Card -->
            <div class="card mb-8 border-l-4 border-yellow-500" data-aos="fade-up">
                <div class="flex items-center">
                    <div class="text-5xl text-yellow-600 ml-6">
                        <i class="fas fa-clock"></i>
                    </div>
                    <div>
                        <h2 class="text-2xl font-bold text-yellow-800 mb-2">القرار قيد المراجعة</h2>
                        <p class="text-gray-700 mb-2">
                            يتم حالياً مراجعة بياناتك من قبل مستشار التوجيه ومجلس الأساتذة
                        </p>
                        <p class="text-sm text-gray-600">
                            سيتم إشعارك فور اتخاذ القرار النهائي
                        </p>
                    </div>
                </div>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Academic Performance -->
                <div class="card mb-6" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">الأداء الأكاديمي</h3>
                    </div>
                    
                    <?php if (!empty($academicResults)): ?>
                        <div class="mb-6">
                            <div class="flex items-center justify-between mb-4">
                                <h4 class="text-lg font-semibold">المعدل العام</h4>
                                <div class="text-3xl font-bold <?php echo $average >= 15 ? 'text-green-600' : ($average >= 12 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                    <?php echo $average; ?>/20
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <?php foreach ($academicResults as $result): ?>
                                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                        <span class="font-medium"><?php echo htmlspecialchars($result['subject']); ?></span>
                                        <span class="font-bold <?php echo $result['grade'] >= 15 ? 'text-green-600' : ($result['grade'] >= 12 ? 'text-yellow-600' : 'text-red-600'); ?>">
                                            <?php echo $result['grade']; ?>/20
                                        </span>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-chart-line text-4xl mb-4"></i>
                            <p>لم يتم إدخال النتائج الأكاديمية بعد</p>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Preferences Analysis -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">تحليل الرغبات</h3>
                    </div>
                    
                    <?php if (!empty($preferences)): ?>
                        <div class="space-y-4">
                            <?php foreach ($preferences as $index => $preference): ?>
                                <div class="flex items-center justify-between p-4 border rounded-lg <?php echo $finalDecision && $finalDecision['assigned_branch_id'] == $preference['branch_id'] ? 'border-green-500 bg-green-50' : 'border-gray-200'; ?>">
                                    <div class="flex items-center">
                                        <div class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold ml-3">
                                            <?php echo $preference['priority_order']; ?>
                                        </div>
                                        <div>
                                            <h4 class="font-semibold"><?php echo htmlspecialchars($preference['branch_name']); ?></h4>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($preference['branch_description']); ?></p>
                                        </div>
                                    </div>
                                    <div class="text-right">
                                        <?php if ($finalDecision && $finalDecision['assigned_branch_id'] == $preference['branch_id']): ?>
                                            <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">
                                                <i class="fas fa-check ml-1"></i>
                                                تم القبول
                                            </span>
                                        <?php else: ?>
                                            <span class="text-sm text-gray-500">
                                                المقاعد المتاحة: <?php echo $preference['available_seats']; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-list-ol text-4xl mb-4"></i>
                            <p>لم يتم تسجيل الرغبات بعد</p>
                            <a href="preferences.php" class="btn btn-primary mt-4">
                                <i class="fas fa-plus ml-2"></i>
                                سجل رغباتك الآن
                            </a>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Completion Status -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">حالة الإكمال</h3>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-list-ol ml-2 text-blue-600"></i>
                                الرغبات
                            </span>
                            <span class="text-sm font-semibold <?php echo !empty($preferences) ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo !empty($preferences) ? 'مكتمل' : 'غير مكتمل'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-clipboard-list ml-2 text-green-600"></i>
                                الاستبيان الشخصي
                            </span>
                            <span class="text-sm font-semibold <?php echo $questionnaireCompleted ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $questionnaireCompleted ? 'مكتمل' : 'غير مكتمل'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-heart ml-2 text-orange-600"></i>
                                استبيان الأسرة
                            </span>
                            <span class="text-sm font-semibold <?php echo $familySurveyCompleted ? 'text-green-600' : 'text-yellow-600'; ?>">
                                <?php echo $familySurveyCompleted ? 'مكتمل' : 'في الانتظار'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-chart-line ml-2 text-purple-600"></i>
                                النتائج الأكاديمية
                            </span>
                            <span class="text-sm font-semibold <?php echo !empty($academicResults) ? 'text-green-600' : 'text-gray-600'; ?>">
                                <?php echo !empty($academicResults) ? 'متوفرة' : 'قيد الإدخال'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Next Steps -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-header">
                        <h3 class="card-title">الخطوات التالية</h3>
                    </div>
                    <div class="space-y-3">
                        <?php if (!$finalDecision): ?>
                            <?php if (empty($preferences)): ?>
                                <a href="preferences.php" class="flex items-center p-3 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                    <i class="fas fa-list-ol text-blue-600 ml-3"></i>
                                    <span class="text-blue-800">سجل رغباتك</span>
                                </a>
                            <?php endif; ?>
                            
                            <?php if (!$questionnaireCompleted): ?>
                                <a href="questionnaire.php" class="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                    <i class="fas fa-clipboard-list text-green-600 ml-3"></i>
                                    <span class="text-green-800">أكمل الاستبيان</span>
                                </a>
                            <?php endif; ?>
                            
                            <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                                <i class="fas fa-clock text-gray-600 ml-3"></i>
                                <span class="text-gray-700">انتظار المراجعة</span>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center p-3 bg-green-50 rounded-lg">
                                <i class="fas fa-check-circle text-green-600 ml-3"></i>
                                <span class="text-green-800">تم الانتهاء من التوجيه</span>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Contact -->
                <div class="card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-header">
                        <h3 class="card-title">تحتاج مساعدة؟</h3>
                    </div>
                    <div class="space-y-3">
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-phone ml-2"></i>
                            تواصل مع مستشار التوجيه
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-envelope ml-2"></i>
                            إرسال استفسار
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-question-circle ml-2"></i>
                            الأسئلة الشائعة
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function printResults() {
    window.print();
}

function exportToPDF() {
    // This would typically call a server-side script to generate PDF
    alert('وظيفة تصدير PDF قيد التطوير');
}
</script>

<?php include '../../includes/footer.php'; ?>
