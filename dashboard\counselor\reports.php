<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require counselor or admin login
if (!hasRole(ROLE_COUNSELOR) && !hasRole(ROLE_ADMIN)) {
    requireRole(ROLE_COUNSELOR);
}

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Handle report generation
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $reportType = $_POST['report_type'] ?? '';
    $format = $_POST['format'] ?? 'html';
    
    switch ($reportType) {
        case 'students_summary':
            generateStudentsSummaryReport($format);
            break;
        case 'guidance_results':
            generateGuidanceResultsReport($format);
            break;
        case 'branch_distribution':
            generateBranchDistributionReport($format);
            break;
        case 'completion_status':
            generateCompletionStatusReport($format);
            break;
    }
}

function generateStudentsSummaryReport($format) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT u.full_name, u.email, u.phone,
               (SELECT COUNT(*) FROM student_preferences sp WHERE sp.student_id = u.id) as preferences_count,
               (SELECT COUNT(*) FROM student_questionnaire sq WHERE sq.student_id = u.id) as questionnaire_completed,
               (SELECT b.name FROM final_decisions fd JOIN branches b ON fd.assigned_branch_id = b.id WHERE fd.student_id = u.id) as assigned_branch
        FROM users u 
        WHERE u.role = 'student' AND u.is_active = 1
        ORDER BY u.full_name
    ");
    $stmt->execute();
    $students = $stmt->fetchAll();
    
    if ($format === 'csv') {
        exportToCSV($students, 'students_summary.csv', [
            'full_name' => 'الاسم الكامل',
            'email' => 'البريد الإلكتروني',
            'phone' => 'الهاتف',
            'preferences_count' => 'عدد الرغبات',
            'questionnaire_completed' => 'الاستبيان مكتمل',
            'assigned_branch' => 'الشعبة المخصصة'
        ]);
    } else {
        displayReport('تقرير ملخص الطلاب', $students, [
            'full_name' => 'الاسم الكامل',
            'email' => 'البريد الإلكتروني',
            'phone' => 'الهاتف',
            'preferences_count' => 'عدد الرغبات',
            'questionnaire_completed' => 'الاستبيان مكتمل',
            'assigned_branch' => 'الشعبة المخصصة'
        ]);
    }
}

function generateGuidanceResultsReport($format) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT u.full_name, b.name as branch_name, fd.algorithm_score, fd.decision_date,
               u2.full_name as decided_by_name
        FROM final_decisions fd
        JOIN users u ON fd.student_id = u.id
        JOIN branches b ON fd.assigned_branch_id = b.id
        LEFT JOIN users u2 ON fd.decided_by = u2.id
        ORDER BY fd.decision_date DESC
    ");
    $stmt->execute();
    $results = $stmt->fetchAll();
    
    if ($format === 'csv') {
        exportToCSV($results, 'guidance_results.csv', [
            'full_name' => 'اسم الطالب',
            'branch_name' => 'الشعبة المخصصة',
            'algorithm_score' => 'نقاط الخوارزمية',
            'decision_date' => 'تاريخ القرار',
            'decided_by_name' => 'متخذ القرار'
        ]);
    } else {
        displayReport('تقرير نتائج التوجيه', $results, [
            'full_name' => 'اسم الطالب',
            'branch_name' => 'الشعبة المخصصة',
            'algorithm_score' => 'نقاط الخوارزمية',
            'decision_date' => 'تاريخ القرار',
            'decided_by_name' => 'متخذ القرار'
        ]);
    }
}

function generateBranchDistributionReport($format) {
    global $pdo;
    
    $stmt = $pdo->prepare("
        SELECT b.name as branch_name, b.available_seats,
               COUNT(fd.id) as assigned_students,
               (b.available_seats - COUNT(fd.id)) as remaining_seats
        FROM branches b
        LEFT JOIN final_decisions fd ON b.id = fd.assigned_branch_id
        WHERE b.is_active = 1
        GROUP BY b.id, b.name, b.available_seats
        ORDER BY b.name
    ");
    $stmt->execute();
    $distribution = $stmt->fetchAll();
    
    if ($format === 'csv') {
        exportToCSV($distribution, 'branch_distribution.csv', [
            'branch_name' => 'اسم الشعبة',
            'available_seats' => 'المقاعد المتاحة',
            'assigned_students' => 'الطلاب المخصصون',
            'remaining_seats' => 'المقاعد المتبقية'
        ]);
    } else {
        displayReport('تقرير توزيع الشعب', $distribution, [
            'branch_name' => 'اسم الشعبة',
            'available_seats' => 'المقاعد المتاحة',
            'assigned_students' => 'الطلاب المخصصون',
            'remaining_seats' => 'المقاعد المتبقية'
        ]);
    }
}

function exportToCSV($data, $filename, $headers) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    
    $output = fopen('php://output', 'w');
    
    // Add BOM for UTF-8
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // Write headers
    fputcsv($output, array_values($headers));
    
    // Write data
    foreach ($data as $row) {
        $csvRow = [];
        foreach (array_keys($headers) as $key) {
            $csvRow[] = $row[$key] ?? '';
        }
        fputcsv($output, $csvRow);
    }
    
    fclose($output);
    exit();
}

function displayReport($title, $data, $headers) {
    // This would display the report in HTML format
    // For now, we'll just redirect back with a message
    redirectWithMessage('reports.php', 'تم إنتاج التقرير بنجاح', 'success');
}

// Get statistics for dashboard
$stats = [];

// Total students
$studentsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'student' AND is_active = 1");
$studentsStmt->execute();
$stats['total_students'] = $studentsStmt->fetch()['count'];

// Students with decisions
$decisionsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM final_decisions");
$decisionsStmt->execute();
$stats['decisions_made'] = $decisionsStmt->fetch()['count'];

// Active branches
$branchesStmt = $pdo->prepare("SELECT COUNT(*) as count FROM branches WHERE is_active = 1");
$branchesStmt->execute();
$stats['active_branches'] = $branchesStmt->fetch()['count'];

$pageTitle = 'التقارير';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">التقارير</h1>
                    <p class="text-gray-600">إنتاج وتصدير التقارير المختلفة للنظام</p>
                </div>
                <div class="text-5xl text-orange-600 opacity-20">
                    <i class="fas fa-file-alt"></i>
                </div>
            </div>
        </div>

        <!-- Flash Message -->
        <?php 
        $flash = getFlashMessage();
        if ($flash):
        ?>
            <div class="alert alert-<?php echo $flash['type']; ?> mb-6" data-aos="fade-up">
                <i class="fas fa-<?php echo $flash['type'] === 'success' ? 'check-circle' : 'exclamation-circle'; ?> ml-2"></i>
                <?php echo htmlspecialchars($flash['message']); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="card text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="text-4xl text-blue-600 mb-3">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['total_students']; ?></h3>
                <p class="text-gray-600">إجمالي الطلاب</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="text-4xl text-green-600 mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['decisions_made']; ?></h3>
                <p class="text-gray-600">قرارات متخذة</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="text-4xl text-purple-600 mb-3">
                    <i class="fas fa-sitemap"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['active_branches']; ?></h3>
                <p class="text-gray-600">شعب نشطة</p>
            </div>
        </div>

        <!-- Report Types -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Students Summary Report -->
            <div class="card" data-aos="fade-up" data-aos-delay="100">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-users text-blue-600 ml-2"></i>
                        تقرير ملخص الطلاب
                    </h3>
                </div>
                <p class="text-gray-600 mb-4">
                    تقرير شامل يحتوي على معلومات جميع الطلاب وحالة إكمال البيانات المطلوبة
                </p>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="students_summary">
                        <input type="hidden" name="format" value="html">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye ml-1"></i>
                            عرض
                        </button>
                    </form>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="students_summary">
                        <input type="hidden" name="format" value="csv">
                        <button type="submit" class="btn btn-outline btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير CSV
                        </button>
                    </form>
                </div>
            </div>

            <!-- Guidance Results Report -->
            <div class="card" data-aos="fade-up" data-aos-delay="200">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-chart-bar text-green-600 ml-2"></i>
                        تقرير نتائج التوجيه
                    </h3>
                </div>
                <p class="text-gray-600 mb-4">
                    تقرير يعرض نتائج التوجيه النهائية للطلاب مع نقاط الخوارزمية وتواريخ القرارات
                </p>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="guidance_results">
                        <input type="hidden" name="format" value="html">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye ml-1"></i>
                            عرض
                        </button>
                    </form>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="guidance_results">
                        <input type="hidden" name="format" value="csv">
                        <button type="submit" class="btn btn-outline btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير CSV
                        </button>
                    </form>
                </div>
            </div>

            <!-- Branch Distribution Report -->
            <div class="card" data-aos="fade-up" data-aos-delay="300">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-sitemap text-purple-600 ml-2"></i>
                        تقرير توزيع الشعب
                    </h3>
                </div>
                <p class="text-gray-600 mb-4">
                    تقرير يوضح توزيع الطلاب على الشعب المختلفة والمقاعد المتاحة والمتبقية
                </p>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="branch_distribution">
                        <input type="hidden" name="format" value="html">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye ml-1"></i>
                            عرض
                        </button>
                    </form>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="branch_distribution">
                        <input type="hidden" name="format" value="csv">
                        <button type="submit" class="btn btn-outline btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير CSV
                        </button>
                    </form>
                </div>
            </div>

            <!-- Completion Status Report -->
            <div class="card" data-aos="fade-up" data-aos-delay="400">
                <div class="card-header">
                    <h3 class="card-title">
                        <i class="fas fa-tasks text-orange-600 ml-2"></i>
                        تقرير حالة الإكمال
                    </h3>
                </div>
                <p class="text-gray-600 mb-4">
                    تقرير يوضح حالة إكمال الطلاب للمتطلبات (الرغبات، الاستبيان، استبيان الأسرة)
                </p>
                <div class="flex items-center space-x-2 space-x-reverse">
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="completion_status">
                        <input type="hidden" name="format" value="html">
                        <button type="submit" class="btn btn-primary btn-sm">
                            <i class="fas fa-eye ml-1"></i>
                            عرض
                        </button>
                    </form>
                    <form method="POST" style="display: inline;">
                        <input type="hidden" name="report_type" value="completion_status">
                        <input type="hidden" name="format" value="csv">
                        <button type="submit" class="btn btn-outline btn-sm">
                            <i class="fas fa-download ml-1"></i>
                            تصدير CSV
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Custom Report Builder -->
        <div class="card mt-8" data-aos="fade-up" data-aos-delay="500">
            <div class="card-header">
                <h3 class="card-title">
                    <i class="fas fa-cogs text-gray-600 ml-2"></i>
                    منشئ التقارير المخصص
                </h3>
            </div>
            <div class="text-center py-8 text-gray-500">
                <i class="fas fa-wrench text-4xl mb-4"></i>
                <h4 class="text-lg font-semibold mb-2">قيد التطوير</h4>
                <p class="mb-4">سيتم إضافة إمكانية إنشاء تقارير مخصصة قريباً</p>
                <button class="btn btn-outline" disabled>
                    <i class="fas fa-plus ml-2"></i>
                    إنشاء تقرير مخصص
                </button>
            </div>
        </div>

        <!-- Export Options -->
        <div class="card mt-8" data-aos="fade-up" data-aos-delay="600">
            <div class="card-header">
                <h3 class="card-title">خيارات التصدير المتاحة</h3>
            </div>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center p-4">
                    <div class="text-3xl text-green-600 mb-2">
                        <i class="fas fa-file-csv"></i>
                    </div>
                    <h4 class="font-semibold mb-1">CSV</h4>
                    <p class="text-sm text-gray-600">ملف بيانات منظم يمكن فتحه في Excel</p>
                </div>
                <div class="text-center p-4 opacity-50">
                    <div class="text-3xl text-red-600 mb-2">
                        <i class="fas fa-file-pdf"></i>
                    </div>
                    <h4 class="font-semibold mb-1">PDF</h4>
                    <p class="text-sm text-gray-600">قيد التطوير</p>
                </div>
                <div class="text-center p-4 opacity-50">
                    <div class="text-3xl text-blue-600 mb-2">
                        <i class="fas fa-file-excel"></i>
                    </div>
                    <h4 class="font-semibold mb-1">Excel</h4>
                    <p class="text-sm text-gray-600">قيد التطوير</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
