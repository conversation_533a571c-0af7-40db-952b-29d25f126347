<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Get file ID from URL
$fileId = (int)($_GET['id'] ?? 0);

if (!$fileId) {
    http_response_code(400);
    die('معرف الملف مطلوب');
}

try {
    // Get file info
    $stmt = $pdo->prepare("
        SELECT * FROM file_uploads 
        WHERE id = ?
    ");
    $stmt->execute([$fileId]);
    $file = $stmt->fetch();
    
    if (!$file) {
        http_response_code(404);
        die('الملف غير موجود');
    }
    
    // Check permissions (users can only download their own files, counselors can download all)
    if ($file['user_id'] != $currentUser['id'] && !hasRole(ROLE_COUNSELOR) && !hasRole(ROLE_ADMIN)) {
        http_response_code(403);
        die('ليس لديك صلاحية لتحميل هذا الملف');
    }
    
    // Check if file exists
    if (!file_exists($file['file_path'])) {
        http_response_code(404);
        die('الملف غير موجود على الخادم');
    }
    
    // Log download activity
    logActivity($currentUser['id'], 'file_download', "Downloaded file: {$file['original_filename']}");
    
    // Set headers for download
    header('Content-Type: ' . $file['file_type']);
    header('Content-Disposition: attachment; filename="' . $file['original_filename'] . '"');
    header('Content-Length: ' . $file['file_size']);
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    
    // Output file
    readfile($file['file_path']);
    exit();
    
} catch (Exception $e) {
    http_response_code(500);
    die('خطأ في تحميل الملف: ' . $e->getMessage());
}
?>
