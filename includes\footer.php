    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-8 mt-12">
        <div class="container">
            <div class="row">
                <div class="col-md-4">
                    <h5 class="text-xl font-semibold mb-4">توجيهي</h5>
                    <p class="text-gray-300">
                        نظام ذكي لإدارة التوجيه المدرسي يساعد في اتخاذ قرارات مدروسة ومنصفة لمستقبل الطلاب.
                    </p>
                </div>
                <div class="col-md-4">
                    <h5 class="text-xl font-semibold mb-4">روابط سريعة</h5>
                    <ul class="space-y-2">
                        <li><a href="<?php echo SITE_URL; ?>/" class="text-gray-300 hover:text-white transition-colors">الرئيسية</a></li>
                        <li><a href="<?php echo SITE_URL; ?>/auth/login.php" class="text-gray-300 hover:text-white transition-colors">تسجيل الدخول</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">حول النظام</a></li>
                        <li><a href="#" class="text-gray-300 hover:text-white transition-colors">المساعدة</a></li>
                    </ul>
                </div>
                <div class="col-md-4">
                    <h5 class="text-xl font-semibold mb-4">معلومات التواصل</h5>
                    <div class="space-y-2 text-gray-300">
                        <p><i class="fas fa-school ml-2"></i><?php echo getSystemSetting('school_name', SCHOOL_NAME); ?></p>
                        <p><i class="fas fa-map-marker-alt ml-2"></i><?php echo getSystemSetting('school_address', SCHOOL_ADDRESS); ?></p>
                        <p><i class="fas fa-phone ml-2"></i><?php echo getSystemSetting('school_phone', SCHOOL_PHONE); ?></p>
                        <p><i class="fas fa-envelope ml-2"></i><?php echo getSystemSetting('school_email', SCHOOL_EMAIL); ?></p>
                    </div>
                </div>
            </div>
            <hr class="my-6 border-gray-600">
            <div class="text-center text-gray-400">
                <p>&copy; <?php echo date('Y'); ?> نظام توجيهي. جميع الحقوق محفوظة.</p>
                <p class="text-sm mt-2">تم التطوير بواسطة فريق التطوير المحلي</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript Files -->
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    
    <!-- Custom JavaScript -->
    <script>
        // Initialize AOS animations
        AOS.init({
            duration: 800,
            easing: 'ease-in-out',
            once: true,
            offset: 100
        });
        
        // Loading overlay functions
        function showLoading() {
            document.getElementById('loadingOverlay').classList.remove('hidden');
            document.getElementById('loadingOverlay').classList.add('flex');
        }
        
        function hideLoading() {
            document.getElementById('loadingOverlay').classList.add('hidden');
            document.getElementById('loadingOverlay').classList.remove('flex');
        }
        
        // Form validation helper
        function validateForm(formId) {
            const form = document.getElementById(formId);
            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;
            
            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                    input.classList.add('is-valid');
                }
            });
            
            return isValid;
        }
        
        // AJAX helper function
        function makeAjaxRequest(url, method = 'GET', data = null) {
            return new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open(method, url, true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        if (xhr.status === 200) {
                            try {
                                const response = JSON.parse(xhr.responseText);
                                resolve(response);
                            } catch (e) {
                                resolve(xhr.responseText);
                            }
                        } else {
                            reject(new Error('Request failed with status: ' + xhr.status));
                        }
                    }
                };
                
                if (data) {
                    xhr.send(JSON.stringify(data));
                } else {
                    xhr.send();
                }
            });
        }
        
        // Confirmation dialog
        function confirmAction(message, callback) {
            if (confirm(message)) {
                callback();
            }
        }
        
        // Auto-resize textareas
        document.addEventListener('DOMContentLoaded', function() {
            const textareas = document.querySelectorAll('textarea');
            textareas.forEach(textarea => {
                textarea.addEventListener('input', function() {
                    this.style.height = 'auto';
                    this.style.height = (this.scrollHeight) + 'px';
                });
            });
        });
        
        // Smooth scroll for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
        
        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const mobileMenu = document.getElementById('mobileMenu');
            if (mobileMenu) {
                mobileMenu.classList.toggle('hidden');
            }
        }
        
        // Print function
        function printPage() {
            window.print();
        }
        
        // Export to PDF (placeholder - would need actual implementation)
        function exportToPDF() {
            alert('وظيفة التصدير إلى PDF قيد التطوير');
        }
        
        // Copy to clipboard
        function copyToClipboard(text) {
            navigator.clipboard.writeText(text).then(function() {
                alert('تم النسخ إلى الحافظة');
            }, function(err) {
                console.error('فشل في النسخ: ', err);
            });
        }
    </script>
    
    <?php if (isset($additionalJS)): ?>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?php echo $js; ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>
    
    <?php if (isset($inlineJS)): ?>
        <script>
            <?php echo $inlineJS; ?>
        </script>
    <?php endif; ?>
</body>
</html>
