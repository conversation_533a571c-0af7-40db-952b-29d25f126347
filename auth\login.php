<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ../dashboard/');
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = sanitizeInput($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    
    if (empty($username) || empty($password)) {
        $error = 'يرجى إدخال اسم المستخدم وكلمة المرور';
    } else {
        try {
            $pdo = getDBConnection();
            $stmt = $pdo->prepare("SELECT * FROM users WHERE (username = ? OR email = ?) AND is_active = 1");
            $stmt->execute([$username, $username]);
            $user = $stmt->fetch();
            
            if ($user && verifyPassword($password, $user['password_hash'])) {
                // Start session and set user data
                startSession();
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['user_role'] = $user['role'];
                $_SESSION['user_name'] = $user['full_name'];
                $_SESSION['username'] = $user['username'];
                
                // Update last login
                $updateStmt = $pdo->prepare("UPDATE users SET last_login = NOW() WHERE id = ?");
                $updateStmt->execute([$user['id']]);
                
                // Log activity
                logActivity($user['id'], 'login', 'User logged in successfully');
                
                // Redirect based on role
                switch ($user['role']) {
                    case ROLE_ADMIN:
                    case ROLE_COUNSELOR:
                        header('Location: ../dashboard/counselor/');
                        break;
                    case ROLE_STUDENT:
                        header('Location: ../dashboard/student/');
                        break;
                    default:
                        header('Location: ../dashboard/');
                }
                exit();
            } else {
                $error = 'اسم المستخدم أو كلمة المرور غير صحيحة';
            }
        } catch (Exception $e) {
            $error = 'حدث خطأ في النظام. يرجى المحاولة مرة أخرى.';
            error_log("Login error: " . $e->getMessage());
        }
    }
}

$pageTitle = 'تسجيل الدخول';
include '../includes/header.php';
?>

<div class="min-h-screen bg-gradient-to-br from-blue-50 to-teal-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <!-- Logo and Title -->
        <div class="text-center" data-aos="fade-down">
            <div class="mx-auto h-20 w-20 bg-gradient-to-r from-teal-600 to-blue-600 rounded-full flex items-center justify-center mb-4">
                <i class="fas fa-graduation-cap text-3xl text-white"></i>
            </div>
            <h2 class="text-3xl font-bold text-gray-900 mb-2">مرحباً بك في توجيهي</h2>
            <p class="text-gray-600">يرجى تسجيل الدخول للوصول إلى حسابك</p>
        </div>

        <!-- Login Form -->
        <div class="card" data-aos="fade-up">
            <?php if ($error): ?>
                <div class="alert alert-error mb-6">
                    <i class="fas fa-exclamation-circle ml-2"></i>
                    <?php echo htmlspecialchars($error); ?>
                </div>
            <?php endif; ?>

            <form method="POST" action="" id="loginForm">
                <div class="form-group">
                    <label for="username" class="form-label">
                        <i class="fas fa-user ml-2"></i>
                        اسم المستخدم أو البريد الإلكتروني
                    </label>
                    <input 
                        type="text" 
                        id="username" 
                        name="username" 
                        class="form-control" 
                        required 
                        value="<?php echo htmlspecialchars($username ?? ''); ?>"
                        placeholder="أدخل اسم المستخدم أو البريد الإلكتروني"
                    >
                </div>

                <div class="form-group">
                    <label for="password" class="form-label">
                        <i class="fas fa-lock ml-2"></i>
                        كلمة المرور
                    </label>
                    <div class="relative">
                        <input 
                            type="password" 
                            id="password" 
                            name="password" 
                            class="form-control" 
                            required 
                            placeholder="أدخل كلمة المرور"
                        >
                        <button 
                            type="button" 
                            class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
                            onclick="togglePassword()"
                        >
                            <i id="passwordToggleIcon" class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="flex items-center justify-between mb-6">
                    <div class="form-check">
                        <input type="checkbox" id="remember" name="remember" class="form-check-input">
                        <label for="remember" class="form-check-label text-sm text-gray-600">
                            تذكرني
                        </label>
                    </div>
                    <a href="forgot-password.php" class="text-sm text-blue-600 hover:text-blue-800">
                        نسيت كلمة المرور؟
                    </a>
                </div>

                <button type="submit" class="btn btn-primary w-full">
                    <i class="fas fa-sign-in-alt ml-2"></i>
                    تسجيل الدخول
                </button>
            </form>
        </div>

        <!-- Demo Accounts -->
        <div class="card bg-gray-50" data-aos="fade-up" data-aos-delay="100">
            <h3 class="text-lg font-semibold mb-4 text-center">حسابات تجريبية</h3>
            <div class="space-y-3 text-sm">
                <div class="flex justify-between items-center p-2 bg-white rounded">
                    <span class="font-medium">مدير النظام:</span>
                    <span class="text-gray-600">admin / password</span>
                </div>
                <div class="flex justify-between items-center p-2 bg-white rounded">
                    <span class="font-medium">مستشار التوجيه:</span>
                    <span class="text-gray-600">counselor / password</span>
                </div>
                <div class="flex justify-between items-center p-2 bg-white rounded">
                    <span class="font-medium">طالب:</span>
                    <span class="text-gray-600">student / password</span>
                </div>
            </div>
        </div>

        <!-- Back to Home -->
        <div class="text-center" data-aos="fade-up" data-aos-delay="200">
            <a href="../" class="text-blue-600 hover:text-blue-800">
                <i class="fas fa-arrow-right ml-2"></i>
                العودة إلى الصفحة الرئيسية
            </a>
        </div>
    </div>
</div>

<script>
function togglePassword() {
    const passwordInput = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// Form validation
document.getElementById('loginForm').addEventListener('submit', function(e) {
    const username = document.getElementById('username').value.trim();
    const password = document.getElementById('password').value;
    
    if (!username || !password) {
        e.preventDefault();
        alert('يرجى إدخال جميع البيانات المطلوبة');
        return false;
    }
    
    // Show loading
    showLoading();
});

// Quick login buttons for demo
function quickLogin(username, password) {
    document.getElementById('username').value = username;
    document.getElementById('password').value = password;
    document.getElementById('loginForm').submit();
}
</script>

<?php include '../includes/footer.php'; ?>
