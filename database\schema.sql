-- Create database
CREATE DATABASE IF NOT EXISTS tawjihi_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE tawjihi_db;

-- Users table (students, counselors, admins)
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('student', 'counselor', 'admin') NOT NULL,
    phone VARCHAR(20),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE,
    last_login TIMESTAMP NULL
);

-- School branches/tracks
CREATE TABLE branches (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VA<PERSON>HA<PERSON>(100) NOT NULL,
    description TEXT,
    available_seats INT NOT NULL DEFAULT 0,
    requirements TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Student preferences
CREATE TABLE student_preferences (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    branch_id INT NOT NULL,
    priority_order INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    UNIQUE KEY unique_student_branch (student_id, branch_id),
    UNIQUE KEY unique_student_priority (student_id, priority_order)
);

-- Student academic results
CREATE TABLE academic_results (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    subject VARCHAR(100) NOT NULL,
    grade DECIMAL(5,2) NOT NULL,
    semester ENUM('first', 'second', 'annual') NOT NULL,
    academic_year VARCHAR(10) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Student personal questionnaire
CREATE TABLE student_questionnaire (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    interests TEXT,
    skills TEXT,
    career_goals TEXT,
    learning_style VARCHAR(50),
    extracurricular_activities TEXT,
    strengths TEXT,
    weaknesses TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Behavior notes
CREATE TABLE behavior_notes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    counselor_id INT NOT NULL,
    note TEXT NOT NULL,
    behavior_type ENUM('positive', 'negative', 'neutral') NOT NULL,
    severity ENUM('low', 'medium', 'high') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (counselor_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Teacher council decisions
CREATE TABLE teacher_council_decisions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    recommended_branch_id INT,
    decision_notes TEXT,
    meeting_date DATE NOT NULL,
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (recommended_branch_id) REFERENCES branches(id) ON DELETE SET NULL,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- Family survey
CREATE TABLE family_survey (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    parent_name VARCHAR(100) NOT NULL,
    parent_phone VARCHAR(20),
    parent_email VARCHAR(100),
    preferred_branch_id INT,
    family_expectations TEXT,
    financial_considerations TEXT,
    support_level ENUM('high', 'medium', 'low') DEFAULT 'medium',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (preferred_branch_id) REFERENCES branches(id) ON DELETE SET NULL
);

-- Final decisions
CREATE TABLE final_decisions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL,
    assigned_branch_id INT NOT NULL,
    algorithm_score DECIMAL(5,2),
    manual_override BOOLEAN DEFAULT FALSE,
    override_reason TEXT,
    decision_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    decided_by INT NOT NULL,
    is_final BOOLEAN DEFAULT FALSE,
    FOREIGN KEY (student_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_branch_id) REFERENCES branches(id) ON DELETE CASCADE,
    FOREIGN KEY (decided_by) REFERENCES users(id) ON DELETE CASCADE
);

-- System settings
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    updated_by INT,
    FOREIGN KEY (updated_by) REFERENCES users(id) ON DELETE SET NULL
);

-- File uploads
CREATE TABLE file_uploads (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    original_filename VARCHAR(255) NOT NULL,
    stored_filename VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size INT NOT NULL,
    file_type VARCHAR(50) NOT NULL,
    upload_purpose VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Insert default admin user
INSERT INTO users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin');

-- Insert default branches
INSERT INTO branches (name, description, available_seats) VALUES 
('العلوم التجريبية', 'شعبة العلوم التجريبية - رياضيات، فيزياء، كيمياء، علوم طبيعية', 30),
('الرياضيات', 'شعبة الرياضيات - رياضيات، فيزياء، علوم طبيعية', 25),
('التقني رياضي', 'شعبة التقني رياضي - رياضيات، فيزياء، تكنولوجيا', 20),
('الآداب والفلسفة', 'شعبة الآداب والفلسفة - فلسفة، أدب عربي، تاريخ وجغرافيا', 35),
('اللغات الأجنبية', 'شعبة اللغات الأجنبية - إنجليزية، فرنسية، أدب عربي', 25);

-- Insert default system settings
INSERT INTO system_settings (setting_key, setting_value, description) VALUES 
('algorithm_weights', '{"personal_preference": 30, "academic_results": 40, "behavior_notes": 10, "teacher_council": 10, "family_survey": 10}', 'أوزان خوارزمية التوجيه'),
('school_name', 'الثانوية النموذجية', 'اسم المؤسسة'),
('school_address', 'العنوان', 'عنوان المؤسسة'),
('school_phone', '0123456789', 'هاتف المؤسسة'),
('school_email', '<EMAIL>', 'بريد المؤسسة'),
('academic_year', '2024-2025', 'السنة الدراسية الحالية'),
('registration_open', '1', 'حالة التسجيل (1 = مفتوح، 0 = مغلق)');
