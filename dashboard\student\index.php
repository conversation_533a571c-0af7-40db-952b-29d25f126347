<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require student login
requireRole(ROLE_STUDENT);

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Get student statistics
$studentId = $currentUser['id'];

// Check if preferences are submitted
$preferencesStmt = $pdo->prepare("SELECT COUNT(*) as count FROM student_preferences WHERE student_id = ?");
$preferencesStmt->execute([$studentId]);
$preferencesCount = $preferencesStmt->fetch()['count'];

// Check if questionnaire is completed
$questionnaireStmt = $pdo->prepare("SELECT COUNT(*) as count FROM student_questionnaire WHERE student_id = ?");
$questionnaireStmt->execute([$studentId]);
$questionnaireCompleted = $questionnaireStmt->fetch()['count'] > 0;

// Check if family survey is completed
$familySurveyStmt = $pdo->prepare("SELECT COUNT(*) as count FROM family_survey WHERE student_id = ?");
$familySurveyStmt->execute([$studentId]);
$familySurveyCompleted = $familySurveyStmt->fetch()['count'] > 0;

// Check final decision
$decisionStmt = $pdo->prepare("
    SELECT fd.*, b.name as branch_name 
    FROM final_decisions fd 
    JOIN branches b ON fd.assigned_branch_id = b.id 
    WHERE fd.student_id = ?
");
$decisionStmt->execute([$studentId]);
$finalDecision = $decisionStmt->fetch();

// Get available branches
$branchesStmt = $pdo->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
$branchesStmt->execute();
$branches = $branchesStmt->fetchAll();

$pageTitle = 'لوحة تحكم الطالب';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Welcome Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="bg-gradient-to-r from-blue-600 to-teal-600 text-white rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold mb-2">مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></h1>
                        <p class="text-blue-100">تابع تقدمك في عملية التوجيه المدرسي</p>
                    </div>
                    <div class="text-6xl opacity-20">
                        <i class="fas fa-user-graduate"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <!-- Preferences Card -->
            <div class="card text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="text-3xl mb-3 <?php echo $preferencesCount > 0 ? 'text-green-600' : 'text-gray-400'; ?>">
                    <i class="fas fa-list-ol"></i>
                </div>
                <h3 class="font-semibold mb-2">رغباتي</h3>
                <p class="text-sm text-gray-600 mb-3">
                    <?php echo $preferencesCount > 0 ? "تم تسجيل $preferencesCount رغبات" : 'لم يتم التسجيل بعد'; ?>
                </p>
                <a href="preferences.php" class="btn <?php echo $preferencesCount > 0 ? 'btn-success' : 'btn-primary'; ?> btn-sm w-full">
                    <?php echo $preferencesCount > 0 ? 'عرض/تعديل' : 'ابدأ الآن'; ?>
                </a>
            </div>

            <!-- Questionnaire Card -->
            <div class="card text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="text-3xl mb-3 <?php echo $questionnaireCompleted ? 'text-green-600' : 'text-gray-400'; ?>">
                    <i class="fas fa-clipboard-list"></i>
                </div>
                <h3 class="font-semibold mb-2">الاستبيان الشخصي</h3>
                <p class="text-sm text-gray-600 mb-3">
                    <?php echo $questionnaireCompleted ? 'تم الإكمال' : 'لم يتم الإكمال بعد'; ?>
                </p>
                <a href="questionnaire.php" class="btn <?php echo $questionnaireCompleted ? 'btn-success' : 'btn-primary'; ?> btn-sm w-full">
                    <?php echo $questionnaireCompleted ? 'عرض/تعديل' : 'ابدأ الآن'; ?>
                </a>
            </div>

            <!-- Family Survey Card -->
            <div class="card text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="text-3xl mb-3 <?php echo $familySurveyCompleted ? 'text-green-600' : 'text-gray-400'; ?>">
                    <i class="fas fa-heart"></i>
                </div>
                <h3 class="font-semibold mb-2">استبيان الأسرة</h3>
                <p class="text-sm text-gray-600 mb-3">
                    <?php echo $familySurveyCompleted ? 'تم الإكمال' : 'في انتظار ولي الأمر'; ?>
                </p>
                <a href="family-survey.php" class="btn <?php echo $familySurveyCompleted ? 'btn-success' : 'btn-secondary'; ?> btn-sm w-full">
                    <?php echo $familySurveyCompleted ? 'عرض' : 'إرسال رابط'; ?>
                </a>
            </div>

            <!-- Results Card -->
            <div class="card text-center" data-aos="fade-up" data-aos-delay="400">
                <div class="text-3xl mb-3 <?php echo $finalDecision ? 'text-green-600' : 'text-gray-400'; ?>">
                    <i class="fas fa-poll"></i>
                </div>
                <h3 class="font-semibold mb-2">النتيجة النهائية</h3>
                <p class="text-sm text-gray-600 mb-3">
                    <?php echo $finalDecision ? 'متوفرة' : 'قيد المراجعة'; ?>
                </p>
                <a href="results.php" class="btn <?php echo $finalDecision ? 'btn-success' : 'btn-secondary'; ?> btn-sm w-full">
                    عرض النتائج
                </a>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Final Decision Alert -->
                <?php if ($finalDecision): ?>
                    <div class="alert alert-success mb-6" data-aos="fade-up">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-2xl ml-3"></i>
                            <div>
                                <h4 class="font-semibold mb-1">تم اتخاذ القرار النهائي!</h4>
                                <p class="mb-0">تم توجيهك إلى شعبة: <strong><?php echo htmlspecialchars($finalDecision['branch_name']); ?></strong></p>
                                <small class="text-green-700">تاريخ القرار: <?php echo formatDate($finalDecision['decision_date']); ?></small>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <!-- Quick Actions -->
                <div class="card mb-6" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">الإجراءات السريعة</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php if ($preferencesCount == 0): ?>
                            <a href="preferences.php" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                                <i class="fas fa-list-ol text-2xl text-blue-600 ml-3"></i>
                                <div>
                                    <h4 class="font-semibold text-blue-800">سجل رغباتك</h4>
                                    <p class="text-sm text-blue-600">اختر الشعب حسب أولويتك</p>
                                </div>
                            </a>
                        <?php endif; ?>

                        <?php if (!$questionnaireCompleted): ?>
                            <a href="questionnaire.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                                <i class="fas fa-clipboard-list text-2xl text-green-600 ml-3"></i>
                                <div>
                                    <h4 class="font-semibold text-green-800">أكمل الاستبيان</h4>
                                    <p class="text-sm text-green-600">أخبرنا عن ميولاتك وقدراتك</p>
                                </div>
                            </a>
                        <?php endif; ?>

                        <a href="academic-results.php" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                            <i class="fas fa-chart-line text-2xl text-purple-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-purple-800">نتائجي الأكاديمية</h4>
                                <p class="text-sm text-purple-600">عرض درجاتك ومعدلاتك</p>
                            </div>
                        </a>

                        <a href="profile.php" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                            <i class="fas fa-user-edit text-2xl text-orange-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-orange-800">الملف الشخصي</h4>
                                <p class="text-sm text-orange-600">تحديث بياناتك الشخصية</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Available Branches -->
                <div class="card" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">الشعب المتاحة</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <?php foreach ($branches as $branch): ?>
                            <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                                <h4 class="font-semibold text-gray-800 mb-2"><?php echo htmlspecialchars($branch['name']); ?></h4>
                                <p class="text-sm text-gray-600 mb-3"><?php echo htmlspecialchars($branch['description']); ?></p>
                                <div class="flex justify-between items-center text-sm">
                                    <span class="text-gray-500">المقاعد المتاحة:</span>
                                    <span class="font-semibold text-blue-600"><?php echo $branch['available_seats']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Progress Summary -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">ملخص التقدم</h3>
                    </div>
                    <div class="space-y-4">
                        <div class="flex items-center justify-between">
                            <span class="text-sm">الرغبات</span>
                            <span class="text-sm font-semibold <?php echo $preferencesCount > 0 ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $preferencesCount > 0 ? 'مكتمل' : 'غير مكتمل'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">الاستبيان الشخصي</span>
                            <span class="text-sm font-semibold <?php echo $questionnaireCompleted ? 'text-green-600' : 'text-red-600'; ?>">
                                <?php echo $questionnaireCompleted ? 'مكتمل' : 'غير مكتمل'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">استبيان الأسرة</span>
                            <span class="text-sm font-semibold <?php echo $familySurveyCompleted ? 'text-green-600' : 'text-yellow-600'; ?>">
                                <?php echo $familySurveyCompleted ? 'مكتمل' : 'في الانتظار'; ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm">القرار النهائي</span>
                            <span class="text-sm font-semibold <?php echo $finalDecision ? 'text-green-600' : 'text-gray-600'; ?>">
                                <?php echo $finalDecision ? 'متوفر' : 'قيد المراجعة'; ?>
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Help & Support -->
                <div class="card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">المساعدة والدعم</h3>
                    </div>
                    <div class="space-y-3">
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-question-circle ml-2"></i>
                            الأسئلة الشائعة
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-book ml-2"></i>
                            دليل الاستخدام
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-envelope ml-2"></i>
                            تواصل معنا
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
