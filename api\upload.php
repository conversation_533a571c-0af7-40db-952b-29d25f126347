<?php
require_once '../config/config.php';
require_once '../config/database.php';
require_once '../includes/functions.php';

// Require login
requireLogin();

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Handle file upload
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $uploadPurpose = sanitizeInput($_POST['purpose'] ?? 'general');
        
        // Create upload directory if it doesn't exist
        $uploadDir = UPLOAD_DIR . date('Y/m/') . '/';
        if (!is_dir($uploadDir)) {
            mkdir($uploadDir, 0755, true);
        }
        
        // Upload file
        $uploadResult = uploadFile($_FILES['file'], $uploadDir);
        
        // Save file info to database
        $insertStmt = $pdo->prepare("
            INSERT INTO file_uploads 
            (user_id, original_filename, stored_filename, file_path, file_size, file_type, upload_purpose) 
            VALUES (?, ?, ?, ?, ?, ?, ?)
        ");
        
        $insertStmt->execute([
            $currentUser['id'],
            $uploadResult['original_name'],
            $uploadResult['stored_name'],
            $uploadResult['file_path'],
            $uploadResult['file_size'],
            $uploadResult['file_type'],
            $uploadPurpose
        ]);
        
        $fileId = $pdo->lastInsertId();
        
        // Log activity
        logActivity($currentUser['id'], 'file_upload', "Uploaded file: {$uploadResult['original_name']}");
        
        sendJsonResponse([
            'success' => true,
            'message' => 'تم رفع الملف بنجاح',
            'file_id' => $fileId,
            'file_name' => $uploadResult['original_name'],
            'file_size' => $uploadResult['file_size']
        ]);
        
    } catch (Exception $e) {
        sendJsonResponse([
            'success' => false,
            'message' => $e->getMessage()
        ], 400);
    }
}

// Handle file deletion
if ($_SERVER['REQUEST_METHOD'] === 'DELETE') {
    parse_str(file_get_contents("php://input"), $data);
    $fileId = (int)($data['file_id'] ?? 0);
    
    try {
        // Get file info
        $stmt = $pdo->prepare("
            SELECT * FROM file_uploads 
            WHERE id = ? AND user_id = ?
        ");
        $stmt->execute([$fileId, $currentUser['id']]);
        $file = $stmt->fetch();
        
        if (!$file) {
            throw new Exception('الملف غير موجود أو ليس لديك صلاحية لحذفه');
        }
        
        // Delete physical file
        if (file_exists($file['file_path'])) {
            unlink($file['file_path']);
        }
        
        // Delete from database
        $deleteStmt = $pdo->prepare("DELETE FROM file_uploads WHERE id = ?");
        $deleteStmt->execute([$fileId]);
        
        // Log activity
        logActivity($currentUser['id'], 'file_delete', "Deleted file: {$file['original_filename']}");
        
        sendJsonResponse([
            'success' => true,
            'message' => 'تم حذف الملف بنجاح'
        ]);
        
    } catch (Exception $e) {
        sendJsonResponse([
            'success' => false,
            'message' => $e->getMessage()
        ], 400);
    }
}

// Handle file listing
if ($_SERVER['REQUEST_METHOD'] === 'GET') {
    $purpose = sanitizeInput($_GET['purpose'] ?? '');
    $page = max(1, (int)($_GET['page'] ?? 1));
    $limit = 20;
    $offset = ($page - 1) * $limit;
    
    try {
        $whereClause = "user_id = ?";
        $params = [$currentUser['id']];
        
        if (!empty($purpose)) {
            $whereClause .= " AND upload_purpose = ?";
            $params[] = $purpose;
        }
        
        // Get files
        $stmt = $pdo->prepare("
            SELECT id, original_filename, file_size, file_type, upload_purpose, created_at
            FROM file_uploads 
            WHERE $whereClause
            ORDER BY created_at DESC
            LIMIT $limit OFFSET $offset
        ");
        $stmt->execute($params);
        $files = $stmt->fetchAll();
        
        // Get total count
        $countStmt = $pdo->prepare("
            SELECT COUNT(*) as total 
            FROM file_uploads 
            WHERE $whereClause
        ");
        $countStmt->execute($params);
        $total = $countStmt->fetch()['total'];
        
        sendJsonResponse([
            'success' => true,
            'files' => $files,
            'total' => $total,
            'page' => $page,
            'pages' => ceil($total / $limit)
        ]);
        
    } catch (Exception $e) {
        sendJsonResponse([
            'success' => false,
            'message' => $e->getMessage()
        ], 500);
    }
}

// Invalid request method
sendJsonResponse([
    'success' => false,
    'message' => 'طريقة الطلب غير صحيحة'
], 405);
?>
