<?php
// AI Algorithm for Student Guidance
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/functions.php';

class GuidanceAlgorithm {
    private $pdo;
    private $weights;
    
    public function __construct() {
        $this->pdo = getDBConnection();
        $this->loadWeights();
    }
    
    private function loadWeights() {
        $weightsJson = getSystemSetting('algorithm_weights', json_encode(DEFAULT_WEIGHTS));
        $this->weights = json_decode($weightsJson, true);
    }
    
    /**
     * Calculate guidance score for a student and branch combination
     */
    public function calculateScore($studentId, $branchId) {
        $score = 0;
        $details = [];
        
        // 1. Personal Preference Score (30%)
        $preferenceScore = $this->getPreferenceScore($studentId, $branchId);
        $score += $preferenceScore * ($this->weights['personal_preference'] / 100);
        $details['preference'] = $preferenceScore;
        
        // 2. Academic Results Score (40%)
        $academicScore = $this->getAcademicScore($studentId, $branchId);
        $score += $academicScore * ($this->weights['academic_results'] / 100);
        $details['academic'] = $academicScore;
        
        // 3. Behavior Notes Score (10%)
        $behaviorScore = $this->getBehaviorScore($studentId);
        $score += $behaviorScore * ($this->weights['behavior_notes'] / 100);
        $details['behavior'] = $behaviorScore;
        
        // 4. Teacher Council Score (10%)
        $teacherScore = $this->getTeacherCouncilScore($studentId, $branchId);
        $score += $teacherScore * ($this->weights['teacher_council'] / 100);
        $details['teacher_council'] = $teacherScore;
        
        // 5. Family Survey Score (10%)
        $familyScore = $this->getFamilyScore($studentId, $branchId);
        $score += $familyScore * ($this->weights['family_survey'] / 100);
        $details['family'] = $familyScore;
        
        return [
            'total_score' => round($score, 2),
            'details' => $details
        ];
    }
    
    /**
     * Get preference score based on student's priority ranking
     */
    private function getPreferenceScore($studentId, $branchId) {
        $stmt = $this->pdo->prepare("
            SELECT priority_order 
            FROM student_preferences 
            WHERE student_id = ? AND branch_id = ?
        ");
        $stmt->execute([$studentId, $branchId]);
        $preference = $stmt->fetch();
        
        if (!$preference) {
            return 0; // Not in preferences
        }
        
        // Convert priority to score (1st choice = 100, 2nd = 80, 3rd = 60, etc.)
        $priority = $preference['priority_order'];
        return max(0, 120 - ($priority * 20));
    }
    
    /**
     * Get academic score based on relevant subjects for the branch
     */
    private function getAcademicScore($studentId, $branchId) {
        // Get branch requirements (simplified mapping)
        $branchSubjects = $this->getBranchSubjects($branchId);
        
        if (empty($branchSubjects)) {
            // If no specific subjects, use overall average
            $stmt = $this->pdo->prepare("
                SELECT AVG(grade) as avg_grade 
                FROM academic_results 
                WHERE student_id = ?
            ");
            $stmt->execute([$studentId]);
            $result = $stmt->fetch();
            return $result ? ($result['avg_grade'] / 20) * 100 : 50;
        }
        
        // Calculate weighted average for relevant subjects
        $placeholders = str_repeat('?,', count($branchSubjects) - 1) . '?';
        $stmt = $this->pdo->prepare("
            SELECT AVG(grade) as avg_grade 
            FROM academic_results 
            WHERE student_id = ? AND subject IN ($placeholders)
        ");
        $stmt->execute(array_merge([$studentId], $branchSubjects));
        $result = $stmt->fetch();
        
        if (!$result || !$result['avg_grade']) {
            return 50; // Default score if no grades available
        }
        
        // Convert grade (0-20) to score (0-100)
        return ($result['avg_grade'] / 20) * 100;
    }
    
    /**
     * Get behavior score based on behavior notes
     */
    private function getBehaviorScore($studentId) {
        $stmt = $this->pdo->prepare("
            SELECT behavior_type, COUNT(*) as count
            FROM behavior_notes 
            WHERE student_id = ? 
            GROUP BY behavior_type
        ");
        $stmt->execute([$studentId]);
        $behaviors = $stmt->fetchAll();
        
        if (empty($behaviors)) {
            return 75; // Default neutral score
        }
        
        $score = 75; // Start with neutral
        foreach ($behaviors as $behavior) {
            switch ($behavior['behavior_type']) {
                case 'positive':
                    $score += $behavior['count'] * 5;
                    break;
                case 'negative':
                    $score -= $behavior['count'] * 10;
                    break;
            }
        }
        
        return max(0, min(100, $score));
    }
    
    /**
     * Get teacher council recommendation score
     */
    private function getTeacherCouncilScore($studentId, $branchId) {
        $stmt = $this->pdo->prepare("
            SELECT recommended_branch_id 
            FROM teacher_council_decisions 
            WHERE student_id = ? 
            ORDER BY meeting_date DESC 
            LIMIT 1
        ");
        $stmt->execute([$studentId]);
        $decision = $stmt->fetch();
        
        if (!$decision) {
            return 50; // No recommendation
        }
        
        return $decision['recommended_branch_id'] == $branchId ? 100 : 25;
    }
    
    /**
     * Get family survey score
     */
    private function getFamilyScore($studentId, $branchId) {
        $stmt = $this->pdo->prepare("
            SELECT preferred_branch_id, support_level 
            FROM family_survey 
            WHERE student_id = ?
        ");
        $stmt->execute([$studentId]);
        $survey = $stmt->fetch();
        
        if (!$survey) {
            return 50; // No survey
        }
        
        $score = 50;
        
        // Branch preference
        if ($survey['preferred_branch_id'] == $branchId) {
            $score += 30;
        }
        
        // Support level
        switch ($survey['support_level']) {
            case 'high':
                $score += 20;
                break;
            case 'medium':
                $score += 10;
                break;
            case 'low':
                $score -= 10;
                break;
        }
        
        return max(0, min(100, $score));
    }
    
    /**
     * Get relevant subjects for a branch (simplified mapping)
     */
    private function getBranchSubjects($branchId) {
        $stmt = $this->pdo->prepare("SELECT name FROM branches WHERE id = ?");
        $stmt->execute([$branchId]);
        $branch = $stmt->fetch();
        
        if (!$branch) return [];
        
        $branchName = $branch['name'];
        
        // Simplified subject mapping
        $subjectMappings = [
            'العلوم التجريبية' => ['الرياضيات', 'الفيزياء', 'الكيمياء', 'علوم طبيعية'],
            'الرياضيات' => ['الرياضيات', 'الفيزياء', 'علوم طبيعية'],
            'التقني رياضي' => ['الرياضيات', 'الفيزياء', 'التكنولوجيا'],
            'الآداب والفلسفة' => ['الفلسفة', 'اللغة العربية', 'التاريخ والجغرافيا'],
            'اللغات الأجنبية' => ['اللغة الإنجليزية', 'اللغة الفرنسية', 'اللغة العربية']
        ];
        
        return $subjectMappings[$branchName] ?? [];
    }
    
    /**
     * Run the complete guidance algorithm for all students
     */
    public function runGuidanceAlgorithm() {
        // Get all active students
        $studentsStmt = $this->pdo->prepare("
            SELECT id FROM users 
            WHERE role = 'student' AND is_active = 1
        ");
        $studentsStmt->execute();
        $students = $studentsStmt->fetchAll();
        
        // Get all active branches
        $branchesStmt = $this->pdo->prepare("
            SELECT id, available_seats FROM branches 
            WHERE is_active = 1
        ");
        $branchesStmt->execute();
        $branches = $branchesStmt->fetchAll();
        
        $results = [];
        
        // Calculate scores for each student-branch combination
        foreach ($students as $student) {
            $studentId = $student['id'];
            $studentScores = [];
            
            foreach ($branches as $branch) {
                $branchId = $branch['id'];
                $scoreData = $this->calculateScore($studentId, $branchId);
                
                $studentScores[] = [
                    'branch_id' => $branchId,
                    'score' => $scoreData['total_score'],
                    'details' => $scoreData['details']
                ];
            }
            
            // Sort by score (highest first)
            usort($studentScores, function($a, $b) {
                return $b['score'] <=> $a['score'];
            });
            
            $results[$studentId] = $studentScores;
        }
        
        // Apply seat constraints and make final assignments
        $assignments = $this->applySeatConstraints($results, $branches);
        
        return $assignments;
    }
    
    /**
     * Apply seat constraints to make final assignments
     */
    private function applySeatConstraints($studentScores, $branches) {
        // Create seat availability tracking
        $availableSeats = [];
        foreach ($branches as $branch) {
            $availableSeats[$branch['id']] = $branch['available_seats'];
        }
        
        $assignments = [];
        $unassigned = [];
        
        // Create a priority queue of all student-branch combinations
        $allCombinations = [];
        foreach ($studentScores as $studentId => $scores) {
            foreach ($scores as $scoreData) {
                $allCombinations[] = [
                    'student_id' => $studentId,
                    'branch_id' => $scoreData['branch_id'],
                    'score' => $scoreData['score'],
                    'details' => $scoreData['details']
                ];
            }
        }
        
        // Sort all combinations by score (highest first)
        usort($allCombinations, function($a, $b) {
            return $b['score'] <=> $a['score'];
        });
        
        // Assign students to branches based on highest scores and seat availability
        foreach ($allCombinations as $combination) {
            $studentId = $combination['student_id'];
            $branchId = $combination['branch_id'];
            
            // Skip if student already assigned
            if (isset($assignments[$studentId])) {
                continue;
            }
            
            // Skip if no seats available
            if ($availableSeats[$branchId] <= 0) {
                continue;
            }
            
            // Assign student to branch
            $assignments[$studentId] = $combination;
            $availableSeats[$branchId]--;
        }
        
        // Track unassigned students
        foreach ($studentScores as $studentId => $scores) {
            if (!isset($assignments[$studentId])) {
                $unassigned[] = $studentId;
            }
        }
        
        return [
            'assignments' => $assignments,
            'unassigned' => $unassigned,
            'remaining_seats' => $availableSeats
        ];
    }
    
    /**
     * Save algorithm results to database
     */
    public function saveResults($assignments, $decidedBy) {
        try {
            $this->pdo->beginTransaction();
            
            foreach ($assignments as $studentId => $assignment) {
                // Check if decision already exists
                $checkStmt = $this->pdo->prepare("
                    SELECT id FROM final_decisions WHERE student_id = ?
                ");
                $checkStmt->execute([$studentId]);
                $existing = $checkStmt->fetch();
                
                if ($existing) {
                    // Update existing decision
                    $updateStmt = $this->pdo->prepare("
                        UPDATE final_decisions 
                        SET assigned_branch_id = ?, algorithm_score = ?, 
                            decision_date = NOW(), decided_by = ?, is_final = 0
                        WHERE student_id = ?
                    ");
                    $updateStmt->execute([
                        $assignment['branch_id'],
                        $assignment['score'],
                        $decidedBy,
                        $studentId
                    ]);
                } else {
                    // Insert new decision
                    $insertStmt = $this->pdo->prepare("
                        INSERT INTO final_decisions 
                        (student_id, assigned_branch_id, algorithm_score, decided_by) 
                        VALUES (?, ?, ?, ?)
                    ");
                    $insertStmt->execute([
                        $studentId,
                        $assignment['branch_id'],
                        $assignment['score'],
                        $decidedBy
                    ]);
                }
            }
            
            $this->pdo->commit();
            return true;
        } catch (Exception $e) {
            $this->pdo->rollBack();
            throw $e;
        }
    }
    
    /**
     * Use OpenRouter AI for enhanced analysis (optional)
     */
    public function getAIRecommendation($studentId, $branchId) {
        // Get student data
        $studentData = $this->getStudentData($studentId);
        $branchData = $this->getBranchData($branchId);
        
        if (!$studentData || !$branchData) {
            return null;
        }
        
        // Prepare prompt for AI
        $prompt = $this->buildAIPrompt($studentData, $branchData);
        
        try {
            $response = $this->callOpenRouterAPI($prompt);
            return $this->parseAIResponse($response);
        } catch (Exception $e) {
            error_log("AI API Error: " . $e->getMessage());
            return null;
        }
    }
    
    private function getStudentData($studentId) {
        $stmt = $this->pdo->prepare("
            SELECT u.full_name,
                   sq.interests, sq.skills, sq.career_goals, sq.learning_style,
                   AVG(ar.grade) as avg_grade
            FROM users u
            LEFT JOIN student_questionnaire sq ON u.id = sq.student_id
            LEFT JOIN academic_results ar ON u.id = ar.student_id
            WHERE u.id = ?
            GROUP BY u.id
        ");
        $stmt->execute([$studentId]);
        return $stmt->fetch();
    }
    
    private function getBranchData($branchId) {
        $stmt = $this->pdo->prepare("
            SELECT name, description, requirements 
            FROM branches 
            WHERE id = ?
        ");
        $stmt->execute([$branchId]);
        return $stmt->fetch();
    }
    
    private function buildAIPrompt($studentData, $branchData) {
        return "تحليل توافق الطالب مع الشعبة:

الطالب: {$studentData['full_name']}
- الاهتمامات: {$studentData['interests']}
- المهارات: {$studentData['skills']}
- الأهداف المهنية: {$studentData['career_goals']}
- أسلوب التعلم: {$studentData['learning_style']}
- المعدل الأكاديمي: {$studentData['avg_grade']}/20

الشعبة: {$branchData['name']}
- الوصف: {$branchData['description']}
- المتطلبات: {$branchData['requirements']}

يرجى تقييم مدى توافق هذا الطالب مع هذه الشعبة من 1 إلى 10 مع تبرير قصير.";
    }
    
    private function callOpenRouterAPI($prompt) {
        $data = [
            'model' => OPENROUTER_MODEL,
            'messages' => [
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'max_tokens' => 200,
            'temperature' => 0.7
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, OPENROUTER_API_URL);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer ' . OPENROUTER_API_KEY
        ]);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        if ($httpCode !== 200) {
            throw new Exception("API request failed with code: $httpCode");
        }
        
        return json_decode($response, true);
    }
    
    private function parseAIResponse($response) {
        if (!isset($response['choices'][0]['message']['content'])) {
            return null;
        }
        
        $content = $response['choices'][0]['message']['content'];
        
        // Extract score and reasoning (simplified parsing)
        preg_match('/(\d+)\/10/', $content, $matches);
        $score = isset($matches[1]) ? (int)$matches[1] : null;
        
        return [
            'ai_score' => $score,
            'ai_reasoning' => $content
        ];
    }
}
?>
