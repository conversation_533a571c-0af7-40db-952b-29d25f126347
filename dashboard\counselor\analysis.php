<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';
require_once '../../includes/ai-algorithm.php';

// Require counselor or admin login
if (!hasRole(ROLE_COUNSELOR) && !hasRole(ROLE_ADMIN)) {
    requireRole(ROLE_COUNSELOR);
}

$currentUser = getCurrentUser();
$pdo = getDBConnection();
$algorithm = new GuidanceAlgorithm();

$message = '';
$messageType = '';
$analysisResults = null;

// Handle algorithm execution
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        switch ($action) {
            case 'run_analysis':
                $analysisResults = $algorithm->runGuidanceAlgorithm();
                $message = 'تم تشغيل خوارزمية التحليل بنجاح!';
                $messageType = 'success';
                break;
                
            case 'save_results':
                if (isset($_POST['analysis_data'])) {
                    $analysisData = json_decode($_POST['analysis_data'], true);
                    $algorithm->saveResults($analysisData['assignments'], $currentUser['id']);
                    $message = 'تم حفظ نتائج التحليل بنجاح!';
                    $messageType = 'success';
                }
                break;
                
            case 'update_weights':
                $weights = [
                    'personal_preference' => (int)$_POST['personal_preference'],
                    'academic_results' => (int)$_POST['academic_results'],
                    'behavior_notes' => (int)$_POST['behavior_notes'],
                    'teacher_council' => (int)$_POST['teacher_council'],
                    'family_survey' => (int)$_POST['family_survey']
                ];
                
                // Validate weights sum to 100
                $totalWeight = array_sum($weights);
                if ($totalWeight !== 100) {
                    throw new Exception('يجب أن يكون مجموع الأوزان 100%');
                }
                
                updateSystemSetting('algorithm_weights', json_encode($weights), $currentUser['id']);
                $message = 'تم تحديث أوزان الخوارزمية بنجاح!';
                $messageType = 'success';
                break;
        }
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get current weights
$weightsJson = getSystemSetting('algorithm_weights', json_encode(DEFAULT_WEIGHTS));
$currentWeights = json_decode($weightsJson, true);

// Get statistics
$stats = [];

// Total students ready for analysis
$readyStmt = $pdo->prepare("
    SELECT COUNT(DISTINCT u.id) as count
    FROM users u
    JOIN student_preferences sp ON u.id = sp.student_id
    JOIN student_questionnaire sq ON u.id = sq.student_id
    WHERE u.role = 'student' AND u.is_active = 1
");
$readyStmt->execute();
$stats['ready_students'] = $readyStmt->fetch()['count'];

// Students with decisions
$decisionsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM final_decisions");
$decisionsStmt->execute();
$stats['decisions_made'] = $decisionsStmt->fetch()['count'];

// Available branches
$branchesStmt = $pdo->prepare("SELECT COUNT(*) as count FROM branches WHERE is_active = 1");
$branchesStmt->execute();
$stats['active_branches'] = $branchesStmt->fetch()['count'];

$pageTitle = 'التحليل الذكي';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">التحليل الذكي</h1>
                    <p class="text-gray-600">تشغيل خوارزمية الذكاء الاصطناعي لتوجيه الطلاب</p>
                </div>
                <div class="text-5xl text-purple-600 opacity-20">
                    <i class="fas fa-brain"></i>
                </div>
            </div>
        </div>

        <!-- Flash Message -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> mb-6" data-aos="fade-up">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?> ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="card text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="text-4xl text-green-600 mb-3">
                    <i class="fas fa-user-check"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['ready_students']; ?></h3>
                <p class="text-gray-600">طلاب جاهزون للتحليل</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="text-4xl text-blue-600 mb-3">
                    <i class="fas fa-sitemap"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['active_branches']; ?></h3>
                <p class="text-gray-600">شعب متاحة</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="text-4xl text-orange-600 mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['decisions_made']; ?></h3>
                <p class="text-gray-600">قرارات متخذة</p>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Algorithm Controls -->
                <div class="card mb-6" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">تشغيل الخوارزمية</h3>
                    </div>
                    
                    <?php if ($stats['ready_students'] > 0): ?>
                        <div class="text-center py-6">
                            <div class="text-6xl text-purple-600 mb-4">
                                <i class="fas fa-robot"></i>
                            </div>
                            <h4 class="text-xl font-semibold mb-4">جاهز لتشغيل التحليل الذكي</h4>
                            <p class="text-gray-600 mb-6">
                                سيتم تحليل بيانات <?php echo $stats['ready_students']; ?> طالب وتوزيعهم على الشعب المتاحة
                            </p>
                            
                            <form method="POST" id="analysisForm">
                                <input type="hidden" name="action" value="run_analysis">
                                <button type="submit" class="btn btn-primary btn-lg" id="runAnalysisBtn">
                                    <i class="fas fa-play ml-2"></i>
                                    تشغيل التحليل الذكي
                                </button>
                            </form>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-exclamation-triangle text-4xl mb-4"></i>
                            <h4 class="text-lg font-semibold mb-2">لا توجد بيانات كافية</h4>
                            <p class="mb-4">يحتاج الطلاب إلى إكمال رغباتهم والاستبيان الشخصي قبل تشغيل التحليل</p>
                            <a href="students.php" class="btn btn-primary">
                                <i class="fas fa-users ml-2"></i>
                                إدارة الطلاب
                            </a>
                        </div>
                    <?php endif; ?>
                </div>

                <!-- Analysis Results -->
                <?php if ($analysisResults): ?>
                    <div class="card mb-6" data-aos="fade-up" data-aos-delay="100">
                        <div class="card-header">
                            <div class="flex items-center justify-between">
                                <h3 class="card-title">نتائج التحليل</h3>
                                <form method="POST" style="display: inline;">
                                    <input type="hidden" name="action" value="save_results">
                                    <input type="hidden" name="analysis_data" value="<?php echo htmlspecialchars(json_encode($analysisResults)); ?>">
                                    <button type="submit" class="btn btn-success btn-sm">
                                        <i class="fas fa-save ml-1"></i>
                                        حفظ النتائج
                                    </button>
                                </form>
                            </div>
                        </div>
                        
                        <!-- Summary -->
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
                            <div class="bg-green-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-green-600">
                                    <?php echo count($analysisResults['assignments']); ?>
                                </div>
                                <p class="text-green-800">طلاب تم توزيعهم</p>
                            </div>
                            <div class="bg-red-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-red-600">
                                    <?php echo count($analysisResults['unassigned']); ?>
                                </div>
                                <p class="text-red-800">طلاب لم يتم توزيعهم</p>
                            </div>
                            <div class="bg-blue-50 p-4 rounded-lg text-center">
                                <div class="text-2xl font-bold text-blue-600">
                                    <?php echo array_sum($analysisResults['remaining_seats']); ?>
                                </div>
                                <p class="text-blue-800">مقاعد متبقية</p>
                            </div>
                        </div>
                        
                        <!-- Detailed Results -->
                        <div class="overflow-x-auto">
                            <table class="table">
                                <thead>
                                    <tr>
                                        <th>الطالب</th>
                                        <th>الشعبة المقترحة</th>
                                        <th>النقاط</th>
                                        <th>تفاصيل التحليل</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    // Get student names for display
                                    $studentIds = array_keys($analysisResults['assignments']);
                                    if (!empty($studentIds)) {
                                        $placeholders = str_repeat('?,', count($studentIds) - 1) . '?';
                                        $studentsStmt = $pdo->prepare("SELECT id, full_name FROM users WHERE id IN ($placeholders)");
                                        $studentsStmt->execute($studentIds);
                                        $studentNames = $studentsStmt->fetchAll(PDO::FETCH_KEY_PAIR);
                                        
                                        // Get branch names
                                        $branchIds = array_unique(array_column($analysisResults['assignments'], 'branch_id'));
                                        $placeholders = str_repeat('?,', count($branchIds) - 1) . '?';
                                        $branchesStmt = $pdo->prepare("SELECT id, name FROM branches WHERE id IN ($placeholders)");
                                        $branchesStmt->execute($branchIds);
                                        $branchNames = $branchesStmt->fetchAll(PDO::FETCH_KEY_PAIR);
                                        
                                        foreach ($analysisResults['assignments'] as $studentId => $assignment):
                                    ?>
                                        <tr>
                                            <td>
                                                <div class="font-semibold">
                                                    <?php echo htmlspecialchars($studentNames[$studentId] ?? 'طالب غير معروف'); ?>
                                                </div>
                                            </td>
                                            <td>
                                                <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-sm font-medium">
                                                    <?php echo htmlspecialchars($branchNames[$assignment['branch_id']] ?? 'شعبة غير معروفة'); ?>
                                                </span>
                                            </td>
                                            <td>
                                                <div class="text-lg font-bold text-green-600">
                                                    <?php echo $assignment['score']; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="text-xs space-y-1">
                                                    <div>رغبة: <?php echo round($assignment['details']['preference'], 1); ?></div>
                                                    <div>أكاديمي: <?php echo round($assignment['details']['academic'], 1); ?></div>
                                                    <div>سلوك: <?php echo round($assignment['details']['behavior'], 1); ?></div>
                                                    <div>مجلس: <?php echo round($assignment['details']['teacher_council'], 1); ?></div>
                                                    <div>أسرة: <?php echo round($assignment['details']['family'], 1); ?></div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php 
                                        endforeach;
                                    }
                                    ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Algorithm Settings -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">إعدادات الخوارزمية</h3>
                    </div>
                    
                    <form method="POST" id="weightsForm">
                        <input type="hidden" name="action" value="update_weights">
                        
                        <div class="space-y-4">
                            <div>
                                <label class="form-label">الرغبة الشخصية</label>
                                <div class="flex items-center">
                                    <input type="range" name="personal_preference" min="0" max="100" 
                                           value="<?php echo $currentWeights['personal_preference']; ?>" 
                                           class="flex-1 ml-3" oninput="updateWeightDisplay(this)">
                                    <span class="font-bold text-blue-600 min-w-12" id="personal_preference_display">
                                        <?php echo $currentWeights['personal_preference']; ?>%
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label">النتائج الأكاديمية</label>
                                <div class="flex items-center">
                                    <input type="range" name="academic_results" min="0" max="100" 
                                           value="<?php echo $currentWeights['academic_results']; ?>" 
                                           class="flex-1 ml-3" oninput="updateWeightDisplay(this)">
                                    <span class="font-bold text-green-600 min-w-12" id="academic_results_display">
                                        <?php echo $currentWeights['academic_results']; ?>%
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label">ملاحظات السلوك</label>
                                <div class="flex items-center">
                                    <input type="range" name="behavior_notes" min="0" max="100" 
                                           value="<?php echo $currentWeights['behavior_notes']; ?>" 
                                           class="flex-1 ml-3" oninput="updateWeightDisplay(this)">
                                    <span class="font-bold text-purple-600 min-w-12" id="behavior_notes_display">
                                        <?php echo $currentWeights['behavior_notes']; ?>%
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label">رأي مجلس الأساتذة</label>
                                <div class="flex items-center">
                                    <input type="range" name="teacher_council" min="0" max="100" 
                                           value="<?php echo $currentWeights['teacher_council']; ?>" 
                                           class="flex-1 ml-3" oninput="updateWeightDisplay(this)">
                                    <span class="font-bold text-orange-600 min-w-12" id="teacher_council_display">
                                        <?php echo $currentWeights['teacher_council']; ?>%
                                    </span>
                                </div>
                            </div>
                            
                            <div>
                                <label class="form-label">استبيان الأسرة</label>
                                <div class="flex items-center">
                                    <input type="range" name="family_survey" min="0" max="100" 
                                           value="<?php echo $currentWeights['family_survey']; ?>" 
                                           class="flex-1 ml-3" oninput="updateWeightDisplay(this)">
                                    <span class="font-bold text-red-600 min-w-12" id="family_survey_display">
                                        <?php echo $currentWeights['family_survey']; ?>%
                                    </span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                            <div class="flex items-center justify-between">
                                <span class="font-medium">المجموع:</span>
                                <span class="font-bold text-lg" id="totalWeight">100%</span>
                            </div>
                        </div>
                        
                        <button type="submit" class="btn btn-primary w-full mt-4">
                            <i class="fas fa-save ml-2"></i>
                            حفظ الإعدادات
                        </button>
                    </form>
                </div>

                <!-- Help -->
                <div class="card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-header">
                        <h3 class="card-title">كيف تعمل الخوارزمية؟</h3>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start">
                            <i class="fas fa-heart text-blue-600 ml-2 mt-1"></i>
                            <p><strong>الرغبة الشخصية:</strong> ترتيب الطالب للشعب حسب أولويته</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-chart-line text-green-600 ml-2 mt-1"></i>
                            <p><strong>النتائج الأكاديمية:</strong> درجات المواد المرتبطة بكل شعبة</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-user-check text-purple-600 ml-2 mt-1"></i>
                            <p><strong>ملاحظات السلوك:</strong> تقييم السلوك والجدية</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-users text-orange-600 ml-2 mt-1"></i>
                            <p><strong>مجلس الأساتذة:</strong> توصيات المعلمين</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-home text-red-600 ml-2 mt-1"></i>
                            <p><strong>استبيان الأسرة:</strong> رأي ودعم الأسرة</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
$inlineJS = '
function updateWeightDisplay(input) {
    const displayElement = document.getElementById(input.name + "_display");
    displayElement.textContent = input.value + "%";
    updateTotalWeight();
}

function updateTotalWeight() {
    const weights = [
        "personal_preference",
        "academic_results", 
        "behavior_notes",
        "teacher_council",
        "family_survey"
    ];
    
    let total = 0;
    weights.forEach(weight => {
        const input = document.querySelector(`input[name="${weight}"]`);
        total += parseInt(input.value);
    });
    
    const totalElement = document.getElementById("totalWeight");
    totalElement.textContent = total + "%";
    
    // Change color based on total
    if (total === 100) {
        totalElement.className = "font-bold text-lg text-green-600";
    } else {
        totalElement.className = "font-bold text-lg text-red-600";
    }
}

// Form validation
document.getElementById("weightsForm").addEventListener("submit", function(e) {
    const total = parseInt(document.getElementById("totalWeight").textContent);
    if (total !== 100) {
        e.preventDefault();
        alert("يجب أن يكون مجموع الأوزان 100%");
        return false;
    }
});

// Analysis form
document.getElementById("analysisForm").addEventListener("submit", function(e) {
    const btn = document.getElementById("runAnalysisBtn");
    btn.disabled = true;
    btn.innerHTML = "<i class=\"fas fa-spinner fa-spin ml-2\"></i>جاري التحليل...";
    showLoading();
});
';

include '../../includes/footer.php'; 
?>
