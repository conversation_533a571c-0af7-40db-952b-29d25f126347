<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require student login
requireRole(ROLE_STUDENT);

$currentUser = getCurrentUser();
$pdo = getDBConnection();
$studentId = $currentUser['id'];

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $preferences = $_POST['preferences'] ?? [];
        
        if (empty($preferences)) {
            throw new Exception('يرجى اختيار الشعب وترتيبها حسب الأولوية');
        }
        
        // Start transaction
        $pdo->beginTransaction();
        
        // Delete existing preferences
        $deleteStmt = $pdo->prepare("DELETE FROM student_preferences WHERE student_id = ?");
        $deleteStmt->execute([$studentId]);
        
        // Insert new preferences
        $insertStmt = $pdo->prepare("INSERT INTO student_preferences (student_id, branch_id, priority_order) VALUES (?, ?, ?)");
        
        foreach ($preferences as $priority => $branchId) {
            if (!empty($branchId)) {
                $insertStmt->execute([$studentId, $branchId, $priority + 1]);
            }
        }
        
        $pdo->commit();
        
        $message = 'تم حفظ رغباتك بنجاح!';
        $messageType = 'success';
        
        // Log activity
        logActivity($studentId, 'preferences_updated', 'Student updated preferences');
        
    } catch (Exception $e) {
        $pdo->rollBack();
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get available branches
$branchesStmt = $pdo->prepare("SELECT * FROM branches WHERE is_active = 1 ORDER BY name");
$branchesStmt->execute();
$branches = $branchesStmt->fetchAll();

// Get current preferences
$preferencesStmt = $pdo->prepare("
    SELECT sp.*, b.name as branch_name 
    FROM student_preferences sp 
    JOIN branches b ON sp.branch_id = b.id 
    WHERE sp.student_id = ? 
    ORDER BY sp.priority_order
");
$preferencesStmt->execute([$studentId]);
$currentPreferences = $preferencesStmt->fetchAll();

$pageTitle = 'رغباتي';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">رغباتي</h1>
                    <p class="text-gray-600">اختر الشعب التي ترغب في الالتحاق بها وقم بترتيبها حسب الأولوية</p>
                </div>
                <div class="text-5xl text-blue-600 opacity-20">
                    <i class="fas fa-list-ol"></i>
                </div>
            </div>
        </div>

        <!-- Flash Message -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> mb-6" data-aos="fade-up">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?> ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Main Form -->
            <div class="col-md-8">
                <div class="card" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">ترتيب الرغبات</h3>
                        <p class="text-sm text-gray-600 mt-1">اسحب وأفلت الشعب لترتيبها حسب أولويتك</p>
                    </div>
                    
                    <form method="POST" id="preferencesForm">
                        <div id="sortablePreferences" class="space-y-3">
                            <?php 
                            // Create array for easy access
                            $preferencesByBranch = [];
                            foreach ($currentPreferences as $pref) {
                                $preferencesByBranch[$pref['branch_id']] = $pref['priority_order'];
                            }
                            
                            // Show current preferences first, then available branches
                            $displayedBranches = [];
                            
                            // First, show current preferences in order
                            foreach ($currentPreferences as $pref): 
                                $displayedBranches[] = $pref['branch_id'];
                            ?>
                                <div class="preference-item bg-white border-2 border-blue-200 rounded-lg p-4 cursor-move" data-branch-id="<?php echo $pref['branch_id']; ?>">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center">
                                            <div class="priority-number bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">
                                                <?php echo $pref['priority_order']; ?>
                                            </div>
                                            <div>
                                                <h4 class="font-semibold text-gray-800"><?php echo htmlspecialchars($pref['branch_name']); ?></h4>
                                                <p class="text-sm text-gray-600">
                                                    <?php 
                                                    $branch = array_filter($branches, function($b) use ($pref) { 
                                                        return $b['id'] == $pref['branch_id']; 
                                                    });
                                                    $branch = reset($branch);
                                                    echo htmlspecialchars($branch['description'] ?? '');
                                                    ?>
                                                </p>
                                            </div>
                                        </div>
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            <i class="fas fa-grip-vertical text-gray-400"></i>
                                            <button type="button" class="text-red-600 hover:text-red-800" onclick="removePreference(this)">
                                                <i class="fas fa-times"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <!-- Add Branch Dropdown -->
                        <div class="mt-6 p-4 bg-gray-50 rounded-lg">
                            <label class="form-label">إضافة شعبة جديدة:</label>
                            <div class="flex items-center space-x-3 space-x-reverse">
                                <select id="newBranchSelect" class="form-control flex-1">
                                    <option value="">اختر شعبة...</option>
                                    <?php foreach ($branches as $branch): ?>
                                        <?php if (!in_array($branch['id'], $displayedBranches)): ?>
                                            <option value="<?php echo $branch['id']; ?>" data-description="<?php echo htmlspecialchars($branch['description']); ?>">
                                                <?php echo htmlspecialchars($branch['name']); ?>
                                            </option>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </select>
                                <button type="button" class="btn btn-primary" onclick="addPreference()">
                                    <i class="fas fa-plus ml-1"></i>
                                    إضافة
                                </button>
                            </div>
                        </div>
                        
                        <!-- Hidden inputs for form submission -->
                        <div id="hiddenInputs"></div>
                        
                        <div class="flex justify-between items-center mt-6 pt-6 border-t">
                            <a href="index.php" class="btn btn-outline">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save ml-2"></i>
                                حفظ الرغبات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Instructions -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">تعليمات</h3>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-600 ml-2 mt-1"></i>
                            <p>اختر الشعب التي ترغب في الالتحاق بها</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-sort text-blue-600 ml-2 mt-1"></i>
                            <p>اسحب وأفلت الشعب لترتيبها حسب أولويتك</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-exclamation-triangle text-yellow-600 ml-2 mt-1"></i>
                            <p>الرقم 1 يعني الأولوية الأولى (الأهم)</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-save text-green-600 ml-2 mt-1"></i>
                            <p>لا تنس حفظ التغييرات بعد الانتهاء</p>
                        </div>
                    </div>
                </div>
                
                <!-- Available Branches Info -->
                <div class="card" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">معلومات الشعب</h3>
                    </div>
                    <div class="space-y-4">
                        <?php foreach ($branches as $branch): ?>
                            <div class="border-b border-gray-100 pb-3 last:border-b-0">
                                <h4 class="font-semibold text-sm text-gray-800 mb-1">
                                    <?php echo htmlspecialchars($branch['name']); ?>
                                </h4>
                                <p class="text-xs text-gray-600 mb-2">
                                    <?php echo htmlspecialchars($branch['description']); ?>
                                </p>
                                <div class="flex justify-between text-xs">
                                    <span class="text-gray-500">المقاعد المتاحة:</span>
                                    <span class="font-semibold text-blue-600"><?php echo $branch['available_seats']; ?></span>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php 
$additionalJS = ['https://cdn.jsdelivr.net/npm/sortablejs@1.15.0/Sortable.min.js'];
$inlineJS = '
// Initialize sortable
let sortable = Sortable.create(document.getElementById("sortablePreferences"), {
    animation: 150,
    ghostClass: "sortable-ghost",
    chosenClass: "sortable-chosen", 
    dragClass: "sortable-drag",
    onEnd: function() {
        updatePriorityNumbers();
        updateHiddenInputs();
    }
});

// Update priority numbers
function updatePriorityNumbers() {
    const items = document.querySelectorAll(".preference-item");
    items.forEach((item, index) => {
        const priorityNumber = item.querySelector(".priority-number");
        priorityNumber.textContent = index + 1;
    });
}

// Update hidden inputs for form submission
function updateHiddenInputs() {
    const hiddenInputsContainer = document.getElementById("hiddenInputs");
    hiddenInputsContainer.innerHTML = "";
    
    const items = document.querySelectorAll(".preference-item");
    items.forEach((item, index) => {
        const branchId = item.dataset.branchId;
        const input = document.createElement("input");
        input.type = "hidden";
        input.name = `preferences[${index}]`;
        input.value = branchId;
        hiddenInputsContainer.appendChild(input);
    });
}

// Add new preference
function addPreference() {
    const select = document.getElementById("newBranchSelect");
    const branchId = select.value;
    const branchName = select.options[select.selectedIndex].text;
    const branchDescription = select.options[select.selectedIndex].dataset.description;
    
    if (!branchId) {
        alert("يرجى اختيار شعبة");
        return;
    }
    
    // Create new preference item
    const container = document.getElementById("sortablePreferences");
    const newItem = document.createElement("div");
    newItem.className = "preference-item bg-white border-2 border-blue-200 rounded-lg p-4 cursor-move";
    newItem.dataset.branchId = branchId;
    
    newItem.innerHTML = `
        <div class="flex items-center justify-between">
            <div class="flex items-center">
                <div class="priority-number bg-blue-600 text-white rounded-full w-8 h-8 flex items-center justify-center font-bold ml-3">
                    ${container.children.length + 1}
                </div>
                <div>
                    <h4 class="font-semibold text-gray-800">${branchName}</h4>
                    <p class="text-sm text-gray-600">${branchDescription}</p>
                </div>
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                <i class="fas fa-grip-vertical text-gray-400"></i>
                <button type="button" class="text-red-600 hover:text-red-800" onclick="removePreference(this)">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
    `;
    
    container.appendChild(newItem);
    
    // Remove from dropdown
    select.options[select.selectedIndex].remove();
    select.value = "";
    
    updateHiddenInputs();
}

// Remove preference
function removePreference(button) {
    const item = button.closest(".preference-item");
    const branchId = item.dataset.branchId;
    const branchName = item.querySelector("h4").textContent;
    const branchDescription = item.querySelector("p").textContent;
    
    // Add back to dropdown
    const select = document.getElementById("newBranchSelect");
    const option = document.createElement("option");
    option.value = branchId;
    option.textContent = branchName;
    option.dataset.description = branchDescription;
    select.appendChild(option);
    
    // Remove item
    item.remove();
    
    updatePriorityNumbers();
    updateHiddenInputs();
}

// Initialize hidden inputs on page load
document.addEventListener("DOMContentLoaded", function() {
    updateHiddenInputs();
});

// Form validation
document.getElementById("preferencesForm").addEventListener("submit", function(e) {
    const items = document.querySelectorAll(".preference-item");
    if (items.length === 0) {
        e.preventDefault();
        alert("يرجى اختيار شعبة واحدة على الأقل");
        return false;
    }
    
    showLoading();
});
';

$additionalCSS = [];
include '../../includes/footer.php'; 
?>
