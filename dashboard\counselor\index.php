<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require counselor or admin login
if (!hasRole(ROLE_COUNSELOR) && !hasRole(ROLE_ADMIN)) {
    requireRole(ROLE_COUNSELOR);
}

$currentUser = getCurrentUser();
$pdo = getDBConnection();

// Get statistics
$stats = [];

// Total students
$studentsStmt = $pdo->prepare("SELECT COUNT(*) as count FROM users WHERE role = 'student' AND is_active = 1");
$studentsStmt->execute();
$stats['total_students'] = $studentsStmt->fetch()['count'];

// Students with preferences
$preferencesStmt = $pdo->prepare("
    SELECT COUNT(DISTINCT student_id) as count 
    FROM student_preferences
");
$preferencesStmt->execute();
$stats['students_with_preferences'] = $preferencesStmt->fetch()['count'];

// Students with questionnaire
$questionnaireStmt = $pdo->prepare("
    SELECT COUNT(*) as count 
    FROM student_questionnaire
");
$questionnaireStmt->execute();
$stats['students_with_questionnaire'] = $questionnaireStmt->fetch()['count'];

// Final decisions made
$decisionsStmt = $pdo->prepare("
    SELECT COUNT(*) as count 
    FROM final_decisions
");
$decisionsStmt->execute();
$stats['final_decisions'] = $decisionsStmt->fetch()['count'];

// Available branches
$branchesStmt = $pdo->prepare("SELECT COUNT(*) as count FROM branches WHERE is_active = 1");
$branchesStmt->execute();
$stats['active_branches'] = $branchesStmt->fetch()['count'];

// Recent activities (last 10)
$activitiesStmt = $pdo->prepare("
    SELECT u.full_name, 'تسجيل رغبات' as activity, sp.created_at
    FROM student_preferences sp
    JOIN users u ON sp.student_id = u.id
    ORDER BY sp.created_at DESC
    LIMIT 5
    
    UNION ALL
    
    SELECT u.full_name, 'إكمال الاستبيان' as activity, sq.created_at
    FROM student_questionnaire sq
    JOIN users u ON sq.student_id = u.id
    ORDER BY sq.created_at DESC
    LIMIT 5
");
$activitiesStmt->execute();
$recentActivities = $activitiesStmt->fetchAll();

// Get pending tasks
$pendingTasks = [];

// Students without preferences
$noPrefStmt = $pdo->prepare("
    SELECT COUNT(*) as count 
    FROM users u 
    LEFT JOIN student_preferences sp ON u.id = sp.student_id 
    WHERE u.role = 'student' AND u.is_active = 1 AND sp.student_id IS NULL
");
$noPrefStmt->execute();
$pendingTasks['no_preferences'] = $noPrefStmt->fetch()['count'];

// Students without questionnaire
$noQuestStmt = $pdo->prepare("
    SELECT COUNT(*) as count 
    FROM users u 
    LEFT JOIN student_questionnaire sq ON u.id = sq.student_id 
    WHERE u.role = 'student' AND u.is_active = 1 AND sq.student_id IS NULL
");
$noQuestStmt->execute();
$pendingTasks['no_questionnaire'] = $noQuestStmt->fetch()['count'];

$pageTitle = 'لوحة تحكم المستشار';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Welcome Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="bg-gradient-to-r from-teal-600 to-blue-600 text-white rounded-lg p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <h1 class="text-2xl font-bold mb-2">مرحباً، <?php echo htmlspecialchars($currentUser['full_name']); ?></h1>
                        <p class="text-blue-100">لوحة تحكم مستشار التوجيه - إدارة عملية التوجيه المدرسي</p>
                    </div>
                    <div class="text-6xl opacity-20">
                        <i class="fas fa-chalkboard-teacher"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistics Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="card text-center" data-aos="fade-up" data-aos-delay="100">
                <div class="text-4xl text-blue-600 mb-3">
                    <i class="fas fa-users"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['total_students']; ?></h3>
                <p class="text-gray-600">إجمالي الطلاب</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="200">
                <div class="text-4xl text-green-600 mb-3">
                    <i class="fas fa-list-ol"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['students_with_preferences']; ?></h3>
                <p class="text-gray-600">سجلوا رغباتهم</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="300">
                <div class="text-4xl text-purple-600 mb-3">
                    <i class="fas fa-clipboard-check"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['students_with_questionnaire']; ?></h3>
                <p class="text-gray-600">أكملوا الاستبيان</p>
            </div>

            <div class="card text-center" data-aos="fade-up" data-aos-delay="400">
                <div class="text-4xl text-orange-600 mb-3">
                    <i class="fas fa-check-circle"></i>
                </div>
                <h3 class="text-2xl font-bold text-gray-800 mb-1"><?php echo $stats['final_decisions']; ?></h3>
                <p class="text-gray-600">قرارات نهائية</p>
            </div>
        </div>

        <div class="row">
            <!-- Main Content -->
            <div class="col-md-8">
                <!-- Quick Actions -->
                <div class="card mb-6" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">الإجراءات السريعة</h3>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <a href="students.php" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors">
                            <i class="fas fa-users text-2xl text-blue-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-blue-800">إدارة الطلاب</h4>
                                <p class="text-sm text-blue-600">عرض وإدارة بيانات الطلاب</p>
                            </div>
                        </a>

                        <a href="branches.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors">
                            <i class="fas fa-sitemap text-2xl text-green-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-green-800">إدارة الشعب</h4>
                                <p class="text-sm text-green-600">إضافة وتعديل الشعب المتاحة</p>
                            </div>
                        </a>

                        <a href="analysis.php" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors">
                            <i class="fas fa-chart-bar text-2xl text-purple-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-purple-800">التحليل الذكي</h4>
                                <p class="text-sm text-purple-600">تشغيل خوارزمية التوجيه</p>
                            </div>
                        </a>

                        <a href="reports.php" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition-colors">
                            <i class="fas fa-file-alt text-2xl text-orange-600 ml-3"></i>
                            <div>
                                <h4 class="font-semibold text-orange-800">التقارير</h4>
                                <p class="text-sm text-orange-600">إنتاج وتصدير التقارير</p>
                            </div>
                        </a>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="card" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">الأنشطة الأخيرة</h3>
                    </div>
                    <?php if (!empty($recentActivities)): ?>
                        <div class="space-y-3">
                            <?php foreach (array_slice($recentActivities, 0, 8) as $activity): ?>
                                <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                    <div class="flex items-center">
                                        <i class="fas fa-user-circle text-gray-400 ml-3"></i>
                                        <div>
                                            <p class="font-medium"><?php echo htmlspecialchars($activity['full_name']); ?></p>
                                            <p class="text-sm text-gray-600"><?php echo htmlspecialchars($activity['activity']); ?></p>
                                        </div>
                                    </div>
                                    <span class="text-sm text-gray-500">
                                        <?php echo formatDate($activity['created_at']); ?>
                                    </span>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-8 text-gray-500">
                            <i class="fas fa-history text-4xl mb-4"></i>
                            <p>لا توجد أنشطة حديثة</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Pending Tasks -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">المهام المعلقة</h3>
                    </div>
                    <div class="space-y-3">
                        <?php if ($pendingTasks['no_preferences'] > 0): ?>
                            <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-exclamation-triangle text-red-600 ml-2"></i>
                                    <span class="text-red-800">لم يسجلوا رغباتهم</span>
                                </div>
                                <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-sm font-bold">
                                    <?php echo $pendingTasks['no_preferences']; ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if ($pendingTasks['no_questionnaire'] > 0): ?>
                            <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                <div class="flex items-center">
                                    <i class="fas fa-clipboard-list text-yellow-600 ml-2"></i>
                                    <span class="text-yellow-800">لم يكملوا الاستبيان</span>
                                </div>
                                <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-sm font-bold">
                                    <?php echo $pendingTasks['no_questionnaire']; ?>
                                </span>
                            </div>
                        <?php endif; ?>

                        <?php if ($pendingTasks['no_preferences'] == 0 && $pendingTasks['no_questionnaire'] == 0): ?>
                            <div class="text-center py-4 text-green-600">
                                <i class="fas fa-check-circle text-2xl mb-2"></i>
                                <p class="font-medium">جميع المهام مكتملة!</p>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- System Status -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-header">
                        <h3 class="card-title">حالة النظام</h3>
                    </div>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-sitemap ml-2 text-blue-600"></i>
                                الشعب النشطة
                            </span>
                            <span class="font-semibold"><?php echo $stats['active_branches']; ?></span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-percentage ml-2 text-green-600"></i>
                                معدل الإكمال
                            </span>
                            <span class="font-semibold">
                                <?php 
                                $completionRate = $stats['total_students'] > 0 ? 
                                    round(($stats['students_with_preferences'] / $stats['total_students']) * 100) : 0;
                                echo $completionRate . '%';
                                ?>
                            </span>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="flex items-center">
                                <i class="fas fa-calendar ml-2 text-purple-600"></i>
                                السنة الدراسية
                            </span>
                            <span class="font-semibold"><?php echo getSystemSetting('academic_year', '2024-2025'); ?></span>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="card" data-aos="fade-up" data-aos-delay="400">
                    <div class="card-header">
                        <h3 class="card-title">روابط سريعة</h3>
                    </div>
                    <div class="space-y-2">
                        <a href="import-students.php" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-upload ml-2"></i>
                            استيراد طلاب من CSV
                        </a>
                        <a href="bulk-actions.php" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-tasks ml-2"></i>
                            إجراءات جماعية
                        </a>
                        <a href="backup.php" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-database ml-2"></i>
                            نسخ احتياطي
                        </a>
                        <?php if (hasRole(ROLE_ADMIN)): ?>
                            <a href="settings.php" class="flex items-center text-blue-600 hover:text-blue-800">
                                <i class="fas fa-cog ml-2"></i>
                                إعدادات النظام
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include '../../includes/footer.php'; ?>
