تصميم الواجهة (Frontend):

1\.	✅ استخدم UI/UX حديث ومتجاوب (Responsive) لكل الشاشات (حاسوب، لوحي، هاتف). عبر استخدام tailwind

2\.	✅ اختر خط عربي جميل واحترافي (مثل Cairo, Amiri, Tajawal).

3\.	✅ استخدم CSS خارجي موحد لتوحيد الألوان والخطوط والمسافات.

4\.	✅ أنشئ ملف style.css أو استخدم SCSS/SASS عند الحاجة.

5\.	✅ ضع شريط التنقل (Navbar) في ملف منفصل (مثلاً navbar.php أو navbar.html) وقم بإدراجه في كل الصفحات.

6\.	✅ استخدم تصميم متناسق لجميع الصفحات (header، footer، ألوان، buttons، forms).

7\.	✅ أنشئ ملف المكررة منفصل لسهولة التكرار.

8\.	•  الألوان المستخدمة من Muted Modern Palette مثل:


الاستخدام	اللون	الكود HEX
الخلفية الأساسية	Misty White	#F7F6F2
خلفية بطاقات أو أقسام	Ash Gray	#E0E1DD
النص الرئيسي	Charcoal	#2E2E2E
العناوين	Slate Blue	#5D6D7E
لون رئيسي (أزرار - تمييز)	Muted Teal	#5EAAA8
لون ثانوي (روابط - Hover)	Dusty Rose	#CEAB93
لون تحذير أو خطأ	Soft Red	#C97C7C
لون نجاح	Desaturated Green	#8FB996



14\.	•  دعم كامل للـ RTL (من اليمين لليسار).

15\.	

التعامل مع البيانات (Backend):

1\.	✅ فصل الأكواد الخلفية في مجلد backend/ بشكل منظم حسب الوظائف (auth، api، db).

2\.	✅ إنشاء ملف اتصال موحد بقاعدة البيانات db.php وتضمينه عند الحاجة.

3\.	✅ تنظيم عمليات المصادقة والتسجيل داخل مجلد auth/.

4\.	✅ فصل كل خدمة أو وظيفة في API بملف مستقل (modular coding).

مميزات إضافية مطلوبة:

1\.	✅ دعم اللغة العربية RTL بشكل كامل.

2\.	✅ وضع صور وهمية وعناوين وهمية قابلة للاستبدال.

3\.	✅ ضمان سرعة تحميل الموقع (كود خفيف، ملفات منظمة).

4\.	✅ توحيد تنسيقات الأزرار، المدخلات، والبطاقات عبر الموقع.

5\.	✅ إنشاء ملف config.php أو .env لحفظ المتغيرات العامة.

الأنيميشن (Animations):

•	استخدام scrolling animation عند دخول العناصر باستخدام مكتبة مثل AOS أو CSS Scroll Effects.

•	استخدام text animation في العناوين والشعارات (باستخدام CSS أو GSAP).

•	الحركات تكون ناعمة وغير مزعجة (ease-in, fade, scale, slide...).

قاعدى بيانات mysql

الموقع للدراسة فقط لا تهتم بالحمالية كثيرا 

i dont hava database for now make a one, i use xammp

make a folder to upload the documents witout chiffremnt 

    // إعدادات OpenRouter.ai API
    $api_key = '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';
    $api_url = 'https://openrouter.ai/api/v1/chat/completions';
    $model = 'deepseek/deepseek-chat:free'; // نموذج DeepSeek V3 