<?php
require_once '../../config/config.php';
require_once '../../config/database.php';
require_once '../../includes/functions.php';

// Require student login
requireRole(ROLE_STUDENT);

$currentUser = getCurrentUser();
$pdo = getDBConnection();
$studentId = $currentUser['id'];

$message = '';
$messageType = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        $interests = sanitizeInput($_POST['interests'] ?? '');
        $skills = sanitizeInput($_POST['skills'] ?? '');
        $career_goals = sanitizeInput($_POST['career_goals'] ?? '');
        $learning_style = sanitizeInput($_POST['learning_style'] ?? '');
        $extracurricular_activities = sanitizeInput($_POST['extracurricular_activities'] ?? '');
        $strengths = sanitizeInput($_POST['strengths'] ?? '');
        $weaknesses = sanitizeInput($_POST['weaknesses'] ?? '');
        
        // Validation
        if (empty($interests) || empty($career_goals) || empty($learning_style)) {
            throw new Exception('يرجى ملء جميع الحقول المطلوبة');
        }
        
        // Check if questionnaire already exists
        $checkStmt = $pdo->prepare("SELECT id FROM student_questionnaire WHERE student_id = ?");
        $checkStmt->execute([$studentId]);
        $existing = $checkStmt->fetch();
        
        if ($existing) {
            // Update existing questionnaire
            $updateStmt = $pdo->prepare("
                UPDATE student_questionnaire 
                SET interests = ?, skills = ?, career_goals = ?, learning_style = ?, 
                    extracurricular_activities = ?, strengths = ?, weaknesses = ?, 
                    updated_at = NOW()
                WHERE student_id = ?
            ");
            $updateStmt->execute([
                $interests, $skills, $career_goals, $learning_style,
                $extracurricular_activities, $strengths, $weaknesses, $studentId
            ]);
            $message = 'تم تحديث الاستبيان بنجاح!';
        } else {
            // Insert new questionnaire
            $insertStmt = $pdo->prepare("
                INSERT INTO student_questionnaire 
                (student_id, interests, skills, career_goals, learning_style, 
                 extracurricular_activities, strengths, weaknesses) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ");
            $insertStmt->execute([
                $studentId, $interests, $skills, $career_goals, $learning_style,
                $extracurricular_activities, $strengths, $weaknesses
            ]);
            $message = 'تم حفظ الاستبيان بنجاح!';
        }
        
        $messageType = 'success';
        
        // Log activity
        logActivity($studentId, 'questionnaire_updated', 'Student updated questionnaire');
        
    } catch (Exception $e) {
        $message = $e->getMessage();
        $messageType = 'error';
    }
}

// Get existing questionnaire data
$questionnaireStmt = $pdo->prepare("SELECT * FROM student_questionnaire WHERE student_id = ?");
$questionnaireStmt->execute([$studentId]);
$questionnaire = $questionnaireStmt->fetch();

$pageTitle = 'الاستبيان الشخصي';
include '../../includes/header.php';
?>

<!-- Navigation -->
<?php include '../../includes/navbar.php'; ?>

<div class="min-h-screen bg-gray-50">
    <div class="container py-8">
        <!-- Header -->
        <div class="mb-8" data-aos="fade-down">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800 mb-2">الاستبيان الشخصي</h1>
                    <p class="text-gray-600">أخبرنا عن ميولاتك وقدراتك لنساعدك في اختيار التوجه المناسب</p>
                </div>
                <div class="text-5xl text-green-600 opacity-20">
                    <i class="fas fa-clipboard-list"></i>
                </div>
            </div>
        </div>

        <!-- Flash Message -->
        <?php if ($message): ?>
            <div class="alert alert-<?php echo $messageType; ?> mb-6" data-aos="fade-up">
                <i class="fas fa-<?php echo $messageType === 'success' ? 'check-circle' : 'exclamation-circle'; ?> ml-2"></i>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Main Form -->
            <div class="col-md-8">
                <div class="card" data-aos="fade-up">
                    <div class="card-header">
                        <h3 class="card-title">معلومات شخصية</h3>
                        <p class="text-sm text-gray-600 mt-1">ساعدنا في فهم شخصيتك وميولاتك بشكل أفضل</p>
                    </div>
                    
                    <form method="POST" id="questionnaireForm">
                        <!-- Interests -->
                        <div class="form-group">
                            <label for="interests" class="form-label">
                                <i class="fas fa-heart ml-2 text-red-500"></i>
                                اهتماماتك وهواياتك *
                            </label>
                            <textarea 
                                id="interests" 
                                name="interests" 
                                class="form-control" 
                                rows="4" 
                                required
                                placeholder="مثال: القراءة، الرياضة، الموسيقى، البرمجة، الرسم..."
                            ><?php echo htmlspecialchars($questionnaire['interests'] ?? ''); ?></textarea>
                            <small class="text-gray-500">اذكر الأنشطة والمواضيع التي تستمتع بها في وقت فراغك</small>
                        </div>

                        <!-- Skills -->
                        <div class="form-group">
                            <label for="skills" class="form-label">
                                <i class="fas fa-tools ml-2 text-blue-500"></i>
                                مهاراتك وقدراتك
                            </label>
                            <textarea 
                                id="skills" 
                                name="skills" 
                                class="form-control" 
                                rows="4"
                                placeholder="مثال: التفكير التحليلي، التواصل، القيادة، حل المشكلات، الإبداع..."
                            ><?php echo htmlspecialchars($questionnaire['skills'] ?? ''); ?></textarea>
                            <small class="text-gray-500">اذكر المهارات التي تشعر أنك تتقنها أو تتميز فيها</small>
                        </div>

                        <!-- Career Goals -->
                        <div class="form-group">
                            <label for="career_goals" class="form-label">
                                <i class="fas fa-bullseye ml-2 text-green-500"></i>
                                أهدافك المهنية المستقبلية *
                            </label>
                            <textarea 
                                id="career_goals" 
                                name="career_goals" 
                                class="form-control" 
                                rows="4" 
                                required
                                placeholder="مثال: أريد أن أصبح طبيباً، مهندساً، معلماً، رجل أعمال..."
                            ><?php echo htmlspecialchars($questionnaire['career_goals'] ?? ''); ?></textarea>
                            <small class="text-gray-500">ما هي المهنة أو المجال الذي تحلم بالعمل فيه مستقبلاً؟</small>
                        </div>

                        <!-- Learning Style -->
                        <div class="form-group">
                            <label for="learning_style" class="form-label">
                                <i class="fas fa-brain ml-2 text-purple-500"></i>
                                أسلوب التعلم المفضل لديك *
                            </label>
                            <select id="learning_style" name="learning_style" class="form-control" required>
                                <option value="">اختر أسلوب التعلم...</option>
                                <option value="visual" <?php echo ($questionnaire['learning_style'] ?? '') === 'visual' ? 'selected' : ''; ?>>
                                    بصري (الصور والرسوم البيانية)
                                </option>
                                <option value="auditory" <?php echo ($questionnaire['learning_style'] ?? '') === 'auditory' ? 'selected' : ''; ?>>
                                    سمعي (الاستماع والمناقشة)
                                </option>
                                <option value="kinesthetic" <?php echo ($questionnaire['learning_style'] ?? '') === 'kinesthetic' ? 'selected' : ''; ?>>
                                    حركي (التطبيق العملي)
                                </option>
                                <option value="reading" <?php echo ($questionnaire['learning_style'] ?? '') === 'reading' ? 'selected' : ''; ?>>
                                    قرائي (القراءة والكتابة)
                                </option>
                            </select>
                            <small class="text-gray-500">كيف تفضل تلقي المعلومات وتعلم أشياء جديدة؟</small>
                        </div>

                        <!-- Extracurricular Activities -->
                        <div class="form-group">
                            <label for="extracurricular_activities" class="form-label">
                                <i class="fas fa-users ml-2 text-orange-500"></i>
                                الأنشطة اللاصفية والتطوعية
                            </label>
                            <textarea 
                                id="extracurricular_activities" 
                                name="extracurricular_activities" 
                                class="form-control" 
                                rows="3"
                                placeholder="مثال: عضو في النادي العلمي، متطوع في الجمعيات الخيرية، مشارك في المسابقات..."
                            ><?php echo htmlspecialchars($questionnaire['extracurricular_activities'] ?? ''); ?></textarea>
                            <small class="text-gray-500">الأنشطة التي شاركت فيها خارج المنهج الدراسي</small>
                        </div>

                        <!-- Strengths -->
                        <div class="form-group">
                            <label for="strengths" class="form-label">
                                <i class="fas fa-star ml-2 text-yellow-500"></i>
                                نقاط القوة لديك
                            </label>
                            <textarea 
                                id="strengths" 
                                name="strengths" 
                                class="form-control" 
                                rows="3"
                                placeholder="مثال: الصبر، المثابرة، الدقة، الإبداع، العمل الجماعي..."
                            ><?php echo htmlspecialchars($questionnaire['strengths'] ?? ''); ?></textarea>
                            <small class="text-gray-500">ما هي الصفات الإيجابية التي تميزك؟</small>
                        </div>

                        <!-- Weaknesses -->
                        <div class="form-group">
                            <label for="weaknesses" class="form-label">
                                <i class="fas fa-exclamation-triangle ml-2 text-red-500"></i>
                                نقاط التحسين
                            </label>
                            <textarea 
                                id="weaknesses" 
                                name="weaknesses" 
                                class="form-control" 
                                rows="3"
                                placeholder="مثال: إدارة الوقت، التحدث أمام الجمهور، التركيز لفترات طويلة..."
                            ><?php echo htmlspecialchars($questionnaire['weaknesses'] ?? ''); ?></textarea>
                            <small class="text-gray-500">ما هي الجوانب التي تشعر أنك تحتاج لتطويرها؟</small>
                        </div>
                        
                        <div class="flex justify-between items-center mt-6 pt-6 border-t">
                            <a href="index.php" class="btn btn-outline">
                                <i class="fas fa-arrow-right ml-2"></i>
                                العودة
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save ml-2"></i>
                                <?php echo $questionnaire ? 'تحديث الاستبيان' : 'حفظ الاستبيان'; ?>
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar -->
            <div class="col-md-4">
                <!-- Progress -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="card-header">
                        <h3 class="card-title">تقدم الاستبيان</h3>
                    </div>
                    <div class="text-center">
                        <div class="text-4xl mb-3 <?php echo $questionnaire ? 'text-green-600' : 'text-gray-400'; ?>">
                            <i class="fas fa-<?php echo $questionnaire ? 'check-circle' : 'clock'; ?>"></i>
                        </div>
                        <p class="font-semibold mb-2">
                            <?php echo $questionnaire ? 'مكتمل' : 'غير مكتمل'; ?>
                        </p>
                        <?php if ($questionnaire): ?>
                            <small class="text-gray-500">
                                آخر تحديث: <?php echo formatDate($questionnaire['updated_at']); ?>
                            </small>
                        <?php endif; ?>
                    </div>
                </div>

                <!-- Tips -->
                <div class="card mb-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="card-header">
                        <h3 class="card-title">نصائح مهمة</h3>
                    </div>
                    <div class="space-y-3 text-sm">
                        <div class="flex items-start">
                            <i class="fas fa-lightbulb text-yellow-500 ml-2 mt-1"></i>
                            <p>كن صادقاً في إجاباتك لنتمكن من مساعدتك بشكل أفضل</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-edit text-blue-500 ml-2 mt-1"></i>
                            <p>يمكنك تعديل إجاباتك في أي وقت</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-clock text-green-500 ml-2 mt-1"></i>
                            <p>خذ وقتك في التفكير قبل الإجابة</p>
                        </div>
                        <div class="flex items-start">
                            <i class="fas fa-question-circle text-purple-500 ml-2 mt-1"></i>
                            <p>لا تتردد في طلب المساعدة من مستشار التوجيه</p>
                        </div>
                    </div>
                </div>

                <!-- Help -->
                <div class="card" data-aos="fade-up" data-aos-delay="300">
                    <div class="card-header">
                        <h3 class="card-title">تحتاج مساعدة؟</h3>
                    </div>
                    <div class="space-y-3">
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-phone ml-2"></i>
                            تواصل مع مستشار التوجيه
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-envelope ml-2"></i>
                            إرسال رسالة
                        </a>
                        <a href="#" class="flex items-center text-blue-600 hover:text-blue-800">
                            <i class="fas fa-book ml-2"></i>
                            دليل الاستبيان
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Form validation
document.getElementById('questionnaireForm').addEventListener('submit', function(e) {
    const requiredFields = ['interests', 'career_goals', 'learning_style'];
    let isValid = true;
    
    requiredFields.forEach(fieldName => {
        const field = document.getElementById(fieldName);
        if (!field.value.trim()) {
            field.classList.add('is-invalid');
            isValid = false;
        } else {
            field.classList.remove('is-invalid');
            field.classList.add('is-valid');
        }
    });
    
    if (!isValid) {
        e.preventDefault();
        alert('يرجى ملء جميع الحقول المطلوبة');
        return false;
    }
    
    showLoading();
});

// Auto-save draft (optional feature)
let autoSaveTimer;
function autoSave() {
    clearTimeout(autoSaveTimer);
    autoSaveTimer = setTimeout(() => {
        // Could implement auto-save functionality here
        console.log('Auto-saving draft...');
    }, 30000); // Save every 30 seconds
}

// Add event listeners for auto-save
document.querySelectorAll('textarea, select').forEach(field => {
    field.addEventListener('input', autoSave);
});
</script>

<?php include '../../includes/footer.php'; ?>
