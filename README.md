# نظام توجيهي - Tawjihi School Guidance System

نظام ذكي ومتطور لإدارة التوجيه المدرسي يساعد في اتخاذ قرارات مدروسة ومنصفة لمستقبل الطلاب باستخدام الذكاء الاصطناعي.

## 🌟 المميزات الرئيسية

### للطلاب
- **تسجيل الرغبات**: اختيار الشعب المفضلة وترتيبها حسب الأولوية
- **الاستبيان الشخصي**: تقييم الميول والقدرات الشخصية
- **متابعة النتائج**: عرض القرار النهائي والتحليل التفصيلي
- **واجهة سهلة الاستخدام**: تصميم حديث ومتجاوب مع الأجهزة المختلفة

### لمستشاري التوجيه
- **إدارة الطلاب**: عرض وإدارة بيانات الطلاب وحالة التوجيه
- **التحليل الذكي**: خوارزمية AI متقدمة لتحليل البيانات واقتراح التوجيه
- **التقارير الشاملة**: إنتاج تقارير مفصلة قابلة للتصدير
- **إدارة الشعب**: تحديد الشعب المتاحة والمقاعد

### للإدارة
- **لوحة تحكم شاملة**: إحصائيات ومتابعة شاملة للنظام
- **إعدادات النظام**: تخصيص النظام حسب احتياجات المدرسة
- **إدارة المستخدمين**: إضافة وإدارة المستخدمين والصلاحيات

## 🚀 التقنيات المستخدمة

- **Backend**: PHP 8.0+, MySQL/MariaDB
- **Frontend**: HTML5, CSS3, JavaScript, Tailwind CSS
- **UI Framework**: مكتبة مخصصة مع دعم RTL
- **AI Integration**: OpenRouter API (اختياري)
- **Animations**: AOS (Animate On Scroll)
- **Icons**: Font Awesome 6

## 📋 متطلبات النظام

- PHP 8.0 أو أحدث
- MySQL 5.7+ أو MariaDB 10.3+
- Apache/Nginx Web Server
- 512MB RAM (الحد الأدنى)
- 100MB مساحة تخزين

## 🛠️ التثبيت والإعداد

### 1. تحميل الملفات
```bash
git clone https://github.com/your-repo/tawjihi-system.git
cd tawjihi-system
```

### 2. إعداد قاعدة البيانات
1. إنشاء قاعدة بيانات جديدة في MySQL
2. تحديث معلومات الاتصال في `config/database.php`
3. تشغيل سكريبت التهيئة:
```bash
php database/init.php
```

### 3. إعداد الملفات
1. نسخ `config/config.example.php` إلى `config/config.php`
2. تحديث الإعدادات حسب البيئة
3. التأكد من صلاحيات مجلد `uploads/`

### 4. إنشاء البيانات التجريبية
```bash
php database/demo-data.php
```

### 5. اختبار النظام
```bash
php tests/system-test.php
```

## 🔐 معلومات الدخول الافتراضية

| الدور | اسم المستخدم | كلمة المرور |
|-------|---------------|-------------|
| مدير النظام | admin | password |
| مستشار التوجيه | counselor | password |
| طالب | student1 | password |

⚠️ **مهم**: يرجى تغيير كلمات المرور الافتراضية فور التثبيت!

## 📊 خوارزمية الذكاء الاصطناعي

يستخدم النظام خوارزمية متقدمة تحلل العوامل التالية:

- **الرغبة الشخصية (30%)**: ترتيب الطالب للشعب
- **النتائج الأكاديمية (40%)**: درجات المواد ذات الصلة
- **ملاحظات السلوك (10%)**: تقييم السلوك والانضباط
- **رأي مجلس الأساتذة (10%)**: توصيات المعلمين
- **استبيان الأسرة (10%)**: رأي ودعم الأسرة

يمكن تخصيص هذه الأوزان من خلال لوحة التحكم.

## 📁 هيكل المشروع

```
tawjihi-system/
├── assets/                 # الملفات الثابتة (CSS, JS, Images)
├── auth/                   # نظام المصادقة
├── config/                 # ملفات الإعداد
├── dashboard/              # لوحات التحكم
│   ├── student/           # واجهة الطلاب
│   └── counselor/         # واجهة المستشارين
├── database/              # قاعدة البيانات والسكريبتات
├── includes/              # الملفات المشتركة
├── tests/                 # اختبارات النظام
├── uploads/               # ملفات المستخدمين
└── api/                   # واجهات برمجة التطبيقات
```

## 🔧 التخصيص والإعدادات

### إعدادات المدرسة
يمكن تخصيص معلومات المدرسة من خلال:
- لوحة تحكم الإدارة > الإعدادات
- تحديث ملف `config/config.php`

### إعدادات الخوارزمية
- تخصيص أوزان العوامل المختلفة
- إضافة معايير جديدة للتقييم
- ربط API الذكاء الاصطناعي الخارجي

### التصميم والواجهة
- تخصيص الألوان في `assets/css/style.css`
- إضافة شعار المدرسة
- تعديل النصوص والرسائل

## 📈 التقارير المتاحة

1. **تقرير ملخص الطلاب**: معلومات شاملة عن جميع الطلاب
2. **تقرير نتائج التوجيه**: القرارات النهائية ونقاط التحليل
3. **تقرير توزيع الشعب**: إحصائيات المقاعد والتوزيع
4. **تقرير حالة الإكمال**: متابعة إكمال المتطلبات

جميع التقارير قابلة للتصدير بصيغة CSV.

## 🔒 الأمان والخصوصية

- تشفير كلمات المرور باستخدام PHP password_hash()
- حماية من هجمات XSS و SQL Injection
- نظام صلاحيات متدرج
- تسجيل العمليات والأنشطة
- حماية ملفات الرفع

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

**خطأ في الاتصال بقاعدة البيانات:**
- تحقق من معلومات الاتصال في `config/database.php`
- تأكد من تشغيل خدمة MySQL

**مشكلة في رفع الملفات:**
- تحقق من صلاحيات مجلد `uploads/`
- راجع إعدادات PHP: `upload_max_filesize` و `post_max_size`

**مشاكل في العرض:**
- تأكد من تفعيل mod_rewrite في Apache
- تحقق من مسارات الملفات الثابتة

## 🤝 المساهمة والتطوير

نرحب بالمساهمات! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## 📞 الدعم والمساعدة

- **الوثائق**: راجع ملفات المساعدة في المجلد `docs/`
- **الأسئلة الشائعة**: متوفرة في لوحة التحكم
- **الدعم التقني**: تواصل مع فريق التطوير

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## 🙏 شكر وتقدير

- فريق التطوير المحلي
- مجتمع PHP المفتوح المصدر
- مكتبات الواجهة المستخدمة

---

**تم التطوير بـ ❤️ لخدمة التعليم في الوطن العربي**

## 📊 إحصائيات المشروع

- **إجمالي الملفات**: 50+ ملف
- **أسطر الكود**: 8000+ سطر
- **اللغات المدعومة**: العربية (RTL)
- **المتصفحات المدعومة**: جميع المتصفحات الحديثة
- **الأجهزة المدعومة**: Desktop, Tablet, Mobile

## 🔄 التحديثات المستقبلية

- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة إدارة المدارس
- [ ] تقارير متقدمة بالرسوم البيانية
- [ ] نظام إشعارات متطور
- [ ] دعم لغات إضافية
- [ ] تحليلات متقدمة بالذكاء الاصطناعي
