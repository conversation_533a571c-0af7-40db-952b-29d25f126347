<?php
// Get current user
$currentUser = getCurrentUser();
$currentPage = basename($_SERVER['PHP_SELF']);
?>

<nav class="bg-white shadow-lg border-b-2 border-blue-100">
    <div class="container">
        <div class="flex justify-between items-center py-4">
            <!-- Logo and Brand -->
            <div class="flex items-center">
                <a href="<?php echo SITE_URL; ?>/" class="flex items-center">
                    <div class="w-10 h-10 bg-gradient-to-r from-teal-600 to-blue-600 rounded-lg flex items-center justify-center ml-3">
                        <i class="fas fa-graduation-cap text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-gray-800">توجيهي</h1>
                        <p class="text-xs text-gray-500">نظام التوجيه المدرسي</p>
                    </div>
                </a>
            </div>

            <!-- Navigation Links -->
            <?php if (isLoggedIn()): ?>
                <div class="hidden md:flex items-center space-x-6 space-x-reverse">
                    <?php if (hasRole(ROLE_STUDENT)): ?>
                        <a href="<?php echo SITE_URL; ?>/dashboard/student/" 
                           class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/student/') !== false ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt ml-1"></i>
                            لوحة التحكم
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/student/preferences.php" 
                           class="nav-link <?php echo $currentPage === 'preferences.php' ? 'active' : ''; ?>">
                            <i class="fas fa-list-ol ml-1"></i>
                            رغباتي
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/student/questionnaire.php" 
                           class="nav-link <?php echo $currentPage === 'questionnaire.php' ? 'active' : ''; ?>">
                            <i class="fas fa-clipboard-list ml-1"></i>
                            الاستبيان
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/student/results.php" 
                           class="nav-link <?php echo $currentPage === 'results.php' ? 'active' : ''; ?>">
                            <i class="fas fa-poll ml-1"></i>
                            النتائج
                        </a>
                    <?php elseif (hasRole(ROLE_COUNSELOR) || hasRole(ROLE_ADMIN)): ?>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/" 
                           class="nav-link <?php echo strpos($_SERVER['REQUEST_URI'], '/counselor/') !== false ? 'active' : ''; ?>">
                            <i class="fas fa-tachometer-alt ml-1"></i>
                            لوحة التحكم
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/students.php" 
                           class="nav-link <?php echo $currentPage === 'students.php' ? 'active' : ''; ?>">
                            <i class="fas fa-users ml-1"></i>
                            الطلاب
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/branches.php" 
                           class="nav-link <?php echo $currentPage === 'branches.php' ? 'active' : ''; ?>">
                            <i class="fas fa-sitemap ml-1"></i>
                            الشعب
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/analysis.php" 
                           class="nav-link <?php echo $currentPage === 'analysis.php' ? 'active' : ''; ?>">
                            <i class="fas fa-chart-bar ml-1"></i>
                            التحليل
                        </a>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/reports.php" 
                           class="nav-link <?php echo $currentPage === 'reports.php' ? 'active' : ''; ?>">
                            <i class="fas fa-file-alt ml-1"></i>
                            التقارير
                        </a>
                        <?php if (hasRole(ROLE_ADMIN)): ?>
                            <a href="<?php echo SITE_URL; ?>/dashboard/counselor/settings.php" 
                               class="nav-link <?php echo $currentPage === 'settings.php' ? 'active' : ''; ?>">
                                <i class="fas fa-cog ml-1"></i>
                                الإعدادات
                            </a>
                        <?php endif; ?>
                    <?php endif; ?>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <!-- Notifications -->
                    <div class="relative">
                        <button class="text-gray-600 hover:text-gray-800 relative">
                            <i class="fas fa-bell text-lg"></i>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">3</span>
                        </button>
                    </div>

                    <!-- User Dropdown -->
                    <div class="relative group">
                        <button class="flex items-center space-x-2 space-x-reverse text-gray-700 hover:text-gray-900">
                            <div class="w-8 h-8 bg-gradient-to-r from-teal-600 to-blue-600 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <span class="hidden md:block font-medium"><?php echo htmlspecialchars($currentUser['full_name']); ?></span>
                            <i class="fas fa-chevron-down text-sm"></i>
                        </button>
                        
                        <!-- Dropdown Menu -->
                        <div class="absolute left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-50">
                            <div class="py-2">
                                <div class="px-4 py-2 border-b border-gray-100">
                                    <p class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($currentUser['full_name']); ?></p>
                                    <p class="text-xs text-gray-500"><?php echo htmlspecialchars($currentUser['email']); ?></p>
                                    <p class="text-xs text-blue-600 mt-1">
                                        <?php 
                                        $roleNames = [
                                            ROLE_ADMIN => 'مدير النظام',
                                            ROLE_COUNSELOR => 'مستشار التوجيه',
                                            ROLE_STUDENT => 'طالب'
                                        ];
                                        echo $roleNames[$currentUser['role']] ?? $currentUser['role'];
                                        ?>
                                    </p>
                                </div>
                                <a href="<?php echo SITE_URL; ?>/dashboard/profile.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-user-edit ml-2"></i>
                                    الملف الشخصي
                                </a>
                                <a href="<?php echo SITE_URL; ?>/dashboard/settings.php" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">
                                    <i class="fas fa-cog ml-2"></i>
                                    الإعدادات
                                </a>
                                <div class="border-t border-gray-100 mt-2 pt-2">
                                    <a href="<?php echo SITE_URL; ?>/auth/logout.php" class="block px-4 py-2 text-sm text-red-600 hover:bg-red-50">
                                        <i class="fas fa-sign-out-alt ml-2"></i>
                                        تسجيل الخروج
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Mobile Menu Button -->
                    <button class="md:hidden text-gray-600 hover:text-gray-800" onclick="toggleMobileMenu()">
                        <i class="fas fa-bars text-lg"></i>
                    </button>
                </div>
            <?php else: ?>
                <!-- Guest Navigation -->
                <div class="flex items-center space-x-4 space-x-reverse">
                    <a href="<?php echo SITE_URL; ?>/auth/login.php" class="btn btn-outline btn-sm">
                        <i class="fas fa-sign-in-alt ml-1"></i>
                        تسجيل الدخول
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Mobile Menu -->
        <?php if (isLoggedIn()): ?>
            <div id="mobileMenu" class="md:hidden hidden border-t border-gray-200 py-4">
                <?php if (hasRole(ROLE_STUDENT)): ?>
                    <a href="<?php echo SITE_URL; ?>/dashboard/student/" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-tachometer-alt ml-2"></i>
                        لوحة التحكم
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/student/preferences.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-list-ol ml-2"></i>
                        رغباتي
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/student/questionnaire.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-clipboard-list ml-2"></i>
                        الاستبيان
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/student/results.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-poll ml-2"></i>
                        النتائج
                    </a>
                <?php elseif (hasRole(ROLE_COUNSELOR) || hasRole(ROLE_ADMIN)): ?>
                    <a href="<?php echo SITE_URL; ?>/dashboard/counselor/" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-tachometer-alt ml-2"></i>
                        لوحة التحكم
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/counselor/students.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-users ml-2"></i>
                        الطلاب
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/counselor/branches.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-sitemap ml-2"></i>
                        الشعب
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/counselor/analysis.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-chart-bar ml-2"></i>
                        التحليل
                    </a>
                    <a href="<?php echo SITE_URL; ?>/dashboard/counselor/reports.php" class="block py-2 text-gray-700 hover:text-blue-600">
                        <i class="fas fa-file-alt ml-2"></i>
                        التقارير
                    </a>
                    <?php if (hasRole(ROLE_ADMIN)): ?>
                        <a href="<?php echo SITE_URL; ?>/dashboard/counselor/settings.php" class="block py-2 text-gray-700 hover:text-blue-600">
                            <i class="fas fa-cog ml-2"></i>
                            الإعدادات
                        </a>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</nav>

<style>
.nav-link {
    @apply text-gray-600 hover:text-blue-600 font-medium transition-colors duration-200 px-3 py-2 rounded-md;
}

.nav-link.active {
    @apply text-blue-600 bg-blue-50;
}

.nav-link:hover {
    @apply bg-gray-50;
}

.nav-link.active:hover {
    @apply bg-blue-100;
}
</style>
