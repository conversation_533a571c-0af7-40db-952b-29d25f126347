/* Tawjihi - School Guidance System Styles */

/* Import Arabic fonts */
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* CSS Variables for Muted Modern Palette */
:root {
    --bg-primary: #F7F6F2;        /* <PERSON> White */
    --bg-secondary: #E0E1DD;      /* Ash Gray */
    --text-primary: #2E2E2E;      /* Charcoal */
    --text-heading: #5D6D7E;      /* Slate Blue */
    --color-primary: #5EAAA8;     /* Muted Teal */
    --color-secondary: #CEAB93;   /* <PERSON> Rose */
    --color-error: #C97C7C;       /* Soft Red */
    --color-success: #8FB996;     /* Desaturated Green */
    --color-warning: #E6B566;     /* Soft Orange */
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
}

body {
    font-family: 'Cairo', 'Tajawal', Arial, sans-serif;
    background-color: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    direction: rtl;
    text-align: right;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    color: var(--text-heading);
    font-weight: 600;
    margin-bottom: var(--spacing-md);
    line-height: 1.3;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1.125rem; }

p {
    margin-bottom: var(--spacing-md);
}

a {
    color: var(--color-primary);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--color-secondary);
}

/* Layout */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 var(--spacing-md);
}

.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -var(--spacing-sm);
}

.col {
    flex: 1;
    padding: 0 var(--spacing-sm);
}

.col-1 { flex: 0 0 8.333333%; }
.col-2 { flex: 0 0 16.666667%; }
.col-3 { flex: 0 0 25%; }
.col-4 { flex: 0 0 33.333333%; }
.col-6 { flex: 0 0 50%; }
.col-8 { flex: 0 0 66.666667%; }
.col-9 { flex: 0 0 75%; }
.col-12 { flex: 0 0 100%; }

/* Cards */
.card {
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.card-header {
    border-bottom: 1px solid var(--bg-secondary);
    padding-bottom: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.card-title {
    margin-bottom: 0;
    color: var(--text-heading);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: var(--spacing-sm) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    text-align: center;
    text-decoration: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 120px;
}

.btn-primary {
    background-color: var(--color-primary);
    color: white;
}

.btn-primary:hover {
    background-color: #4a9896;
    color: white;
}

.btn-secondary {
    background-color: var(--color-secondary);
    color: white;
}

.btn-secondary:hover {
    background-color: #c19a7d;
    color: white;
}

.btn-success {
    background-color: var(--color-success);
    color: white;
}

.btn-success:hover {
    background-color: #7da885;
    color: white;
}

.btn-danger {
    background-color: var(--color-error);
    color: white;
}

.btn-danger:hover {
    background-color: #b86b6b;
    color: white;
}

.btn-outline {
    background-color: transparent;
    border: 2px solid var(--color-primary);
    color: var(--color-primary);
}

.btn-outline:hover {
    background-color: var(--color-primary);
    color: white;
}

.btn-sm {
    padding: var(--spacing-xs) var(--spacing-md);
    font-size: 0.875rem;
    min-width: 80px;
}

.btn-lg {
    padding: var(--spacing-md) var(--spacing-xl);
    font-size: 1.125rem;
    min-width: 160px;
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--text-heading);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--bg-secondary);
    border-radius: var(--radius-md);
    font-family: inherit;
    font-size: 1rem;
    background-color: white;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--color-primary);
    box-shadow: 0 0 0 3px rgba(94, 170, 168, 0.1);
}

.form-control.is-invalid {
    border-color: var(--color-error);
}

.form-control.is-valid {
    border-color: var(--color-success);
}

.invalid-feedback {
    display: block;
    color: var(--color-error);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
}

.valid-feedback {
    display: block;
    color: var(--color-success);
    font-size: 0.875rem;
    margin-top: var(--spacing-xs);
}

/* Select */
select.form-control {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: left 0.75rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-left: 2.5rem;
}

/* Checkbox and Radio */
.form-check {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-sm);
}

.form-check-input {
    margin-left: var(--spacing-sm);
    margin-right: 0;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table th,
.table td {
    padding: var(--spacing-md);
    text-align: right;
    border-bottom: 1px solid var(--bg-secondary);
}

.table th {
    background-color: var(--bg-secondary);
    font-weight: 600;
    color: var(--text-heading);
}

.table tbody tr:hover {
    background-color: rgba(94, 170, 168, 0.05);
}

/* Alerts */
.alert {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-lg);
    border: 1px solid transparent;
}

.alert-success {
    background-color: rgba(143, 185, 150, 0.1);
    border-color: var(--color-success);
    color: #2d5a32;
}

.alert-error,
.alert-danger {
    background-color: rgba(201, 124, 124, 0.1);
    border-color: var(--color-error);
    color: #7a2e2e;
}

.alert-warning {
    background-color: rgba(230, 181, 102, 0.1);
    border-color: var(--color-warning);
    color: #8b5a00;
}

.alert-info {
    background-color: rgba(94, 170, 168, 0.1);
    border-color: var(--color-primary);
    color: #2d5a59;
}

/* Utilities */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.d-none { display: none; }
.d-block { display: block; }
.d-flex { display: flex; }
.d-inline-block { display: inline-block; }

.justify-content-center { justify-content: center; }
.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* Responsive */
@media (max-width: 768px) {
    .container {
        padding: 0 var(--spacing-sm);
    }
    
    .col-sm-12 {
        flex: 0 0 100%;
    }
    
    .btn {
        width: 100%;
        margin-bottom: var(--spacing-sm);
    }
    
    h1 { font-size: 2rem; }
    h2 { font-size: 1.75rem; }
    h3 { font-size: 1.5rem; }
}

@media (max-width: 480px) {
    html {
        font-size: 14px;
    }
    
    .card {
        padding: var(--spacing-md);
    }
}

/* Animation classes for AOS */
.fade-in {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.6s ease-out;
}

.fade-in.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.slide-up {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.8s ease-out;
}

.slide-up.aos-animate {
    opacity: 1;
    transform: translateY(0);
}

.scale-in {
    opacity: 0;
    transform: scale(0.9);
    transition: all 0.6s ease-out;
}

.scale-in.aos-animate {
    opacity: 1;
    transform: scale(1);
}

/* Enhanced animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% { transform: translate3d(0,0,0); }
    40%, 43% { transform: translate3d(0, -30px, 0); }
    70% { transform: translate3d(0, -15px, 0); }
    90% { transform: translate3d(0, -4px, 0); }
}

@keyframes slideInRight {
    from { transform: translate3d(100%, 0, 0); visibility: visible; }
    to { transform: translate3d(0, 0, 0); }
}

@keyframes slideInLeft {
    from { transform: translate3d(-100%, 0, 0); visibility: visible; }
    to { transform: translate3d(0, 0, 0); }
}

@keyframes fadeInUp {
    from { opacity: 0; transform: translate3d(0, 100%, 0); }
    to { opacity: 1; transform: translate3d(0, 0, 0); }
}

@keyframes zoomIn {
    from { opacity: 0; transform: scale3d(0.3, 0.3, 0.3); }
    50% { opacity: 1; }
}

.animate-pulse { animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite; }
.animate-bounce { animation: bounce 1s infinite; }
.animate-slideInRight { animation: slideInRight 0.5s ease-out; }
.animate-slideInLeft { animation: slideInLeft 0.5s ease-out; }
.animate-fadeInUp { animation: fadeInUp 0.6s ease-out; }
.animate-zoomIn { animation: zoomIn 0.6s ease-out; }

/* Hover effects */
.hover-lift {
    transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
}

.hover-scale {
    transition: transform var(--transition-fast);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: box-shadow var(--transition-fast);
}

.hover-glow:hover {
    box-shadow: 0 0 20px rgba(94, 170, 168, 0.3);
}

/* Loading states */
.loading {
    position: relative;
    overflow: hidden;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    right: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { right: -100%; }
    100% { right: 100%; }
}

/* Skeleton loading */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

/* Progress bars */
.progress {
    width: 100%;
    height: 8px;
    background-color: var(--bg-secondary);
    border-radius: var(--radius-lg);
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary), var(--color-secondary));
    border-radius: var(--radius-lg);
    transition: width 0.6s ease;
    position: relative;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background-image: linear-gradient(
        -45deg,
        rgba(255, 255, 255, .2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, .2) 50%,
        rgba(255, 255, 255, .2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 50px 50px;
    animation: move 2s linear infinite;
}

@keyframes move {
    0% { background-position: 0 0; }
    100% { background-position: 50px 50px; }
}

/* Notification styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    max-width: 400px;
    padding: var(--spacing-md);
    background: white;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    border-left: 4px solid var(--color-primary);
    transform: translateX(100%);
    transition: transform var(--transition-normal);
}

.notification.show {
    transform: translateX(0);
}

.notification.success { border-left-color: var(--color-success); }
.notification.error { border-left-color: var(--color-error); }
.notification.warning { border-left-color: var(--color-warning); }

/* Tooltip */
.tooltip {
    position: relative;
    display: inline-block;
}

.tooltip .tooltiptext {
    visibility: hidden;
    width: 120px;
    background-color: var(--text-primary);
    color: white;
    text-align: center;
    border-radius: var(--radius-md);
    padding: 5px;
    position: absolute;
    z-index: 1;
    bottom: 125%;
    left: 50%;
    margin-left: -60px;
    opacity: 0;
    transition: opacity var(--transition-fast);
    font-size: 0.875rem;
}

.tooltip .tooltiptext::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: var(--text-primary) transparent transparent transparent;
}

.tooltip:hover .tooltiptext {
    visibility: visible;
    opacity: 1;
}

/* Modal enhancements */
.modal-backdrop {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

.modal-content {
    animation: slideInUp 0.3s ease-out;
}

@keyframes slideInUp {
    from {
        transform: translate3d(0, 100%, 0);
        opacity: 0;
    }
    to {
        transform: translate3d(0, 0, 0);
        opacity: 1;
    }
}

/* Enhanced form styles */
.form-floating {
    position: relative;
}

.form-floating input,
.form-floating textarea {
    padding: 1rem 0.75rem 0.5rem;
}

.form-floating label {
    position: absolute;
    top: 0;
    left: 0.75rem;
    height: 100%;
    padding: 1rem 0;
    pointer-events: none;
    border: 1px solid transparent;
    transform-origin: 0 0;
    transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
}

.form-floating input:focus ~ label,
.form-floating input:not(:placeholder-shown) ~ label,
.form-floating textarea:focus ~ label,
.form-floating textarea:not(:placeholder-shown) ~ label {
    opacity: 0.65;
    transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-left: 8px;
}

.status-online { background-color: var(--color-success); }
.status-offline { background-color: var(--color-error); }
.status-away { background-color: var(--color-warning); }

/* Breadcrumb */
.breadcrumb {
    display: flex;
    flex-wrap: wrap;
    padding: 0;
    margin-bottom: var(--spacing-lg);
    list-style: none;
}

.breadcrumb-item {
    display: flex;
}

.breadcrumb-item + .breadcrumb-item {
    padding-left: var(--spacing-sm);
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "/";
    padding-right: var(--spacing-sm);
    color: var(--text-primary);
    opacity: 0.5;
}

.breadcrumb-item.active {
    color: var(--text-primary);
    opacity: 0.7;
}
