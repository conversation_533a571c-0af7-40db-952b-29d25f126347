<?php
// Tawjihi System Setup Wizard
session_start();

// Check if already installed
if (file_exists('config/config.php') && !isset($_GET['force'])) {
    $configContent = file_get_contents('config/config.php');
    if (strpos($configContent, 'SETUP_COMPLETE') !== false) {
        header('Location: index.php');
        exit();
    }
}

$step = (int)($_GET['step'] ?? 1);
$errors = [];
$success = [];

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    switch ($step) {
        case 1:
            // System Requirements Check
            $step = 2;
            break;
            
        case 2:
            // Database Configuration
            $dbHost = trim($_POST['db_host'] ?? '');
            $dbName = trim($_POST['db_name'] ?? '');
            $dbUser = trim($_POST['db_user'] ?? '');
            $dbPass = $_POST['db_pass'] ?? '';
            
            if (empty($dbHost) || empty($dbName) || empty($dbUser)) {
                $errors[] = 'يرجى ملء جميع حقول قاعدة البيانات المطلوبة';
            } else {
                try {
                    $pdo = new PDO("mysql:host=$dbHost;charset=utf8mb4", $dbUser, $dbPass);
                    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
                    
                    // Create database if it doesn't exist
                    $pdo->exec("CREATE DATABASE IF NOT EXISTS `$dbName` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                    
                    // Store database config in session
                    $_SESSION['db_config'] = [
                        'host' => $dbHost,
                        'name' => $dbName,
                        'user' => $dbUser,
                        'pass' => $dbPass
                    ];
                    
                    $success[] = 'تم الاتصال بقاعدة البيانات بنجاح';
                    $step = 3;
                } catch (Exception $e) {
                    $errors[] = 'خطأ في الاتصال بقاعدة البيانات: ' . $e->getMessage();
                }
            }
            break;
            
        case 3:
            // School Information
            $schoolName = trim($_POST['school_name'] ?? '');
            $schoolAddress = trim($_POST['school_address'] ?? '');
            $schoolPhone = trim($_POST['school_phone'] ?? '');
            $schoolEmail = trim($_POST['school_email'] ?? '');
            
            if (empty($schoolName)) {
                $errors[] = 'اسم المدرسة مطلوب';
            } else {
                $_SESSION['school_config'] = [
                    'name' => $schoolName,
                    'address' => $schoolAddress,
                    'phone' => $schoolPhone,
                    'email' => $schoolEmail
                ];
                $step = 4;
            }
            break;
            
        case 4:
            // Admin Account
            $adminUsername = trim($_POST['admin_username'] ?? '');
            $adminEmail = trim($_POST['admin_email'] ?? '');
            $adminName = trim($_POST['admin_name'] ?? '');
            $adminPassword = $_POST['admin_password'] ?? '';
            $adminPasswordConfirm = $_POST['admin_password_confirm'] ?? '';
            
            if (empty($adminUsername) || empty($adminEmail) || empty($adminName) || empty($adminPassword)) {
                $errors[] = 'جميع حقول المدير مطلوبة';
            } elseif ($adminPassword !== $adminPasswordConfirm) {
                $errors[] = 'كلمات المرور غير متطابقة';
            } elseif (strlen($adminPassword) < 6) {
                $errors[] = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
            } else {
                $_SESSION['admin_config'] = [
                    'username' => $adminUsername,
                    'email' => $adminEmail,
                    'name' => $adminName,
                    'password' => $adminPassword
                ];
                $step = 5;
            }
            break;
            
        case 5:
            // Final Installation
            try {
                installSystem();
                $step = 6;
            } catch (Exception $e) {
                $errors[] = 'خطأ في التثبيت: ' . $e->getMessage();
            }
            break;
    }
}

function installSystem() {
    $dbConfig = $_SESSION['db_config'];
    $schoolConfig = $_SESSION['school_config'];
    $adminConfig = $_SESSION['admin_config'];
    
    // Create config file
    $configContent = "<?php
// Tawjihi System Configuration
// Generated by Setup Wizard on " . date('Y-m-d H:i:s') . "

// Database Configuration
define('DB_HOST', '{$dbConfig['host']}');
define('DB_NAME', '{$dbConfig['name']}');
define('DB_USER', '{$dbConfig['user']}');
define('DB_PASS', '{$dbConfig['pass']}');
define('DB_CHARSET', 'utf8mb4');

// Site Configuration
define('SITE_NAME', 'نظام توجيهي');
define('SITE_URL', 'http://' . \$_SERVER['HTTP_HOST'] . dirname(\$_SERVER['SCRIPT_NAME']));

// School Information
define('SCHOOL_NAME', '{$schoolConfig['name']}');
define('SCHOOL_ADDRESS', '{$schoolConfig['address']}');
define('SCHOOL_PHONE', '{$schoolConfig['phone']}');
define('SCHOOL_EMAIL', '{$schoolConfig['email']}');

// File Upload Configuration
define('UPLOAD_DIR', 'uploads/');
define('MAX_FILE_SIZE', 10 * 1024 * 1024); // 10MB
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']);

// Pagination
define('ITEMS_PER_PAGE', 20);

// User Roles
define('ROLE_ADMIN', 'admin');
define('ROLE_COUNSELOR', 'counselor');
define('ROLE_STUDENT', 'student');

// AI Algorithm Default Weights
define('DEFAULT_WEIGHTS', [
    'personal_preference' => 30,
    'academic_results' => 40,
    'behavior_notes' => 10,
    'teacher_council' => 10,
    'family_survey' => 10
]);

// OpenRouter AI Configuration (Optional)
define('OPENROUTER_API_KEY', '');
define('OPENROUTER_API_URL', 'https://openrouter.ai/api/v1/chat/completions');
define('OPENROUTER_MODEL', 'anthropic/claude-3-haiku');

// Security
define('SESSION_TIMEOUT', 3600); // 1 hour

// Setup Complete Flag
define('SETUP_COMPLETE', true);
?>";

    if (!file_put_contents('config/config.php', $configContent)) {
        throw new Exception('فشل في إنشاء ملف الإعداد');
    }
    
    // Initialize database
    require_once 'config/config.php';
    require_once 'config/database.php';
    
    $pdo = getDBConnection();
    
    // Run schema
    $schema = file_get_contents('database/schema.sql');
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    foreach ($statements as $statement) {
        if (!empty($statement) && !preg_match('/^(CREATE DATABASE|USE)/i', $statement)) {
            $pdo->exec($statement);
        }
    }
    
    // Create admin user
    require_once 'includes/functions.php';
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password_hash, full_name, role) 
        VALUES (?, ?, ?, ?, 'admin')
    ");
    $stmt->execute([
        $adminConfig['username'],
        $adminConfig['email'],
        password_hash($adminConfig['password'], PASSWORD_DEFAULT),
        $adminConfig['name']
    ]);
    
    // Insert school settings
    $settings = [
        ['school_name', $schoolConfig['name']],
        ['school_address', $schoolConfig['address']],
        ['school_phone', $schoolConfig['phone']],
        ['school_email', $schoolConfig['email']],
        ['academic_year', '2024-2025'],
        ['system_version', '1.0.0']
    ];
    
    $settingStmt = $pdo->prepare("
        INSERT INTO system_settings (setting_key, setting_value, updated_by) 
        VALUES (?, ?, 1)
    ");
    
    foreach ($settings as $setting) {
        $settingStmt->execute($setting);
    }
    
    // Create upload directory
    if (!is_dir('uploads')) {
        mkdir('uploads', 0755, true);
    }
    
    // Clear session
    session_destroy();
}

// System Requirements Check
function checkRequirements() {
    $requirements = [
        'PHP Version >= 8.0' => version_compare(PHP_VERSION, '8.0.0', '>='),
        'PDO Extension' => extension_loaded('pdo'),
        'PDO MySQL Extension' => extension_loaded('pdo_mysql'),
        'JSON Extension' => extension_loaded('json'),
        'Config Directory Writable' => is_writable('config'),
        'Upload Directory Writable' => is_writable('.') || is_writable('uploads')
    ];
    
    return $requirements;
}

$requirements = checkRequirements();
$allRequirementsMet = !in_array(false, $requirements);
?>

<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>إعداد نظام توجيهي</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        .rtl { direction: rtl; }
        .step-active { background: linear-gradient(135deg, #0ea5e9, #06b6d4); color: white; }
        .step-completed { background: #10b981; color: white; }
        .step-pending { background: #e5e7eb; color: #6b7280; }
    </style>
</head>
<body class="rtl bg-gray-50">
    <div class="min-h-screen py-12">
        <div class="max-w-4xl mx-auto px-4">
            <!-- Header -->
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-gradient-to-r from-teal-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-graduation-cap text-2xl text-white"></i>
                </div>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">إعداد نظام توجيهي</h1>
                <p class="text-gray-600">مرحباً بك في معالج إعداد نظام التوجيه المدرسي</p>
            </div>

            <!-- Progress Steps -->
            <div class="flex justify-center mb-8">
                <div class="flex items-center space-x-4 space-x-reverse">
                    <?php
                    $steps = [
                        1 => 'متطلبات النظام',
                        2 => 'قاعدة البيانات', 
                        3 => 'معلومات المدرسة',
                        4 => 'حساب المدير',
                        5 => 'التثبيت',
                        6 => 'اكتمال'
                    ];
                    
                    foreach ($steps as $num => $title) {
                        $class = $num < $step ? 'step-completed' : ($num == $step ? 'step-active' : 'step-pending');
                        echo "<div class='flex flex-col items-center'>";
                        echo "<div class='w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold $class'>$num</div>";
                        echo "<span class='text-xs mt-1 text-center'>$title</span>";
                        echo "</div>";
                        if ($num < 6) echo "<div class='w-8 h-px bg-gray-300 mt-5'></div>";
                    }
                    ?>
                </div>
            </div>

            <!-- Main Content -->
            <div class="bg-white rounded-lg shadow-lg p-8">
                <?php if (!empty($errors)): ?>
                    <div class="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle text-red-600 ml-2"></i>
                            <div>
                                <?php foreach ($errors as $error): ?>
                                    <p class="text-red-800"><?php echo htmlspecialchars($error); ?></p>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 ml-2"></i>
                            <div>
                                <?php foreach ($success as $msg): ?>
                                    <p class="text-green-800"><?php echo htmlspecialchars($msg); ?></p>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php if ($step == 1): ?>
                    <!-- Step 1: System Requirements -->
                    <h2 class="text-2xl font-bold mb-6">فحص متطلبات النظام</h2>
                    
                    <div class="space-y-4 mb-8">
                        <?php foreach ($requirements as $req => $met): ?>
                            <div class="flex items-center justify-between p-4 border rounded-lg <?php echo $met ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'; ?>">
                                <span class="font-medium"><?php echo $req; ?></span>
                                <span class="<?php echo $met ? 'text-green-600' : 'text-red-600'; ?>">
                                    <i class="fas fa-<?php echo $met ? 'check' : 'times'; ?>"></i>
                                    <?php echo $met ? 'متوفر' : 'غير متوفر'; ?>
                                </span>
                            </div>
                        <?php endforeach; ?>
                    </div>

                    <?php if ($allRequirementsMet): ?>
                        <form method="POST">
                            <button type="submit" class="w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-arrow-left ml-2"></i>
                                متابعة إلى الخطوة التالية
                            </button>
                        </form>
                    <?php else: ?>
                        <div class="text-center">
                            <p class="text-red-600 mb-4">يرجى حل المشاكل المذكورة أعلاه قبل المتابعة</p>
                            <button onclick="location.reload()" class="bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors">
                                <i class="fas fa-refresh ml-2"></i>
                                إعادة فحص
                            </button>
                        </div>
                    <?php endif; ?>

                <?php elseif ($step == 2): ?>
                    <!-- Step 2: Database Configuration -->
                    <h2 class="text-2xl font-bold mb-6">إعداد قاعدة البيانات</h2>
                    
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">خادم قاعدة البيانات</label>
                            <input type="text" name="db_host" value="localhost" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم قاعدة البيانات</label>
                            <input type="text" name="db_name" value="tawjihi_db" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم</label>
                            <input type="text" name="db_user" value="root" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور</label>
                            <input type="password" name="db_pass" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="?step=1" class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors text-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </a>
                            <button type="submit" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-arrow-left ml-2"></i>
                                التالي
                            </button>
                        </div>
                    </form>

                <?php elseif ($step == 3): ?>
                    <!-- Step 3: School Information -->
                    <h2 class="text-2xl font-bold mb-6">معلومات المدرسة</h2>
                    
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المدرسة *</label>
                            <input type="text" name="school_name" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">عنوان المدرسة</label>
                            <input type="text" name="school_address" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">رقم الهاتف</label>
                            <input type="tel" name="school_phone" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني</label>
                            <input type="email" name="school_email" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="?step=2" class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors text-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </a>
                            <button type="submit" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-arrow-left ml-2"></i>
                                التالي
                            </button>
                        </div>
                    </form>

                <?php elseif ($step == 4): ?>
                    <!-- Step 4: Admin Account -->
                    <h2 class="text-2xl font-bold mb-6">إنشاء حساب المدير</h2>
                    
                    <form method="POST" class="space-y-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">اسم المستخدم *</label>
                            <input type="text" name="admin_username" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">البريد الإلكتروني *</label>
                            <input type="email" name="admin_email" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">الاسم الكامل *</label>
                            <input type="text" name="admin_name" required 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">كلمة المرور *</label>
                            <input type="password" name="admin_password" required minlength="6"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">تأكيد كلمة المرور *</label>
                            <input type="password" name="admin_password_confirm" required minlength="6"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                        </div>
                        
                        <div class="flex space-x-4 space-x-reverse">
                            <a href="?step=3" class="flex-1 bg-gray-600 text-white py-3 px-6 rounded-lg hover:bg-gray-700 transition-colors text-center">
                                <i class="fas fa-arrow-right ml-2"></i>
                                السابق
                            </a>
                            <button type="submit" class="flex-1 bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-arrow-left ml-2"></i>
                                التالي
                            </button>
                        </div>
                    </form>

                <?php elseif ($step == 5): ?>
                    <!-- Step 5: Installation -->
                    <h2 class="text-2xl font-bold mb-6">تثبيت النظام</h2>
                    
                    <div class="text-center mb-8">
                        <div class="text-6xl text-blue-600 mb-4">
                            <i class="fas fa-cog fa-spin"></i>
                        </div>
                        <p class="text-lg text-gray-600 mb-4">جاري تثبيت النظام...</p>
                        <p class="text-sm text-gray-500">هذا قد يستغرق بضع ثوانٍ</p>
                    </div>
                    
                    <form method="POST">
                        <button type="submit" class="w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors">
                            <i class="fas fa-play ml-2"></i>
                            بدء التثبيت
                        </button>
                    </form>

                <?php elseif ($step == 6): ?>
                    <!-- Step 6: Completion -->
                    <div class="text-center">
                        <div class="text-6xl text-green-600 mb-6">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-gray-800 mb-4">تم التثبيت بنجاح!</h2>
                        <p class="text-lg text-gray-600 mb-8">نظام توجيهي جاهز للاستخدام الآن</p>
                        
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
                            <h3 class="text-lg font-semibold text-blue-800 mb-4">معلومات الدخول</h3>
                            <div class="text-left">
                                <p><strong>اسم المستخدم:</strong> <?php echo htmlspecialchars($_SESSION['admin_config']['username'] ?? 'admin'); ?></p>
                                <p><strong>كلمة المرور:</strong> [كلمة المرور التي أدخلتها]</p>
                            </div>
                        </div>
                        
                        <div class="space-y-4">
                            <a href="index.php" class="block w-full bg-blue-600 text-white py-3 px-6 rounded-lg hover:bg-blue-700 transition-colors">
                                <i class="fas fa-home ml-2"></i>
                                الذهاب إلى الصفحة الرئيسية
                            </a>
                            <a href="auth/login.php" class="block w-full bg-green-600 text-white py-3 px-6 rounded-lg hover:bg-green-700 transition-colors">
                                <i class="fas fa-sign-in-alt ml-2"></i>
                                تسجيل الدخول
                            </a>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</body>
</html>
